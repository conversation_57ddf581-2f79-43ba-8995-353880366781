# 许可证创建和验证工作流程示例

## 1. 创建许可证

### 单个创建

```json
POST /api/licenses
{
  "versionId": 1,
  "distributorId": 2,
  "verifyInstance": {
    "expiration": "2024-12-31",
    "maxDevices": 3,
    "features": ["feature1", "feature2"],
    "customField": "custom value"
  }
}
```

### 批量创建

```json
POST /api/licenses
{
  "versionId": 1,
  "distributorId": 2,
  "quantity": 5,
  "verifyInstance": {
    "expiration": "2024-12-31",
    "maxDevices": 3,
    "features": ["feature1", "feature2"],
    "customField": "custom value"
  }
}
```

## 2. 客户端验证流程

### 步骤 1：客户端发送验证请求

```json
POST /api/verify
{
  "licenseKey": "ABCD-1234-EFGH-5678",
  "verifyInstance": "client_device_info_or_fingerprint"
}
```

### 步骤 2：服务端返回验证配置

```json
{
  "success": true,
  "data": {
    "valid": true,
    "productInfo": {
      "id": 1,
      "name": "MyProduct",
      "version": "1.0.0"
    },
    "verifyInstance": {
      "expiration": "2024-12-31",
      "maxDevices": 3,
      "features": ["feature1", "feature2"],
      "customField": "custom value"
    },
    "activatedAt": "2024-01-01T00:00:00Z",
    "message": "许可证激活成功"
  }
}
```

### 步骤 3：客户端本地验证

客户端收到配置后，在本地执行验证逻辑：

```javascript
function validateLicense(config, clientInfo) {
  // 检查过期时间
  if (new Date() > new Date(config.expiration)) {
    return { valid: false, reason: "expired" };
  }

  // 检查设备数量限制
  if (clientInfo.deviceCount > config.maxDevices) {
    return { valid: false, reason: "device_limit_exceeded" };
  }

  // 检查功能权限
  for (const feature of clientInfo.requestedFeatures) {
    if (!config.features.includes(feature)) {
      return { valid: false, reason: "feature_not_authorized" };
    }
  }

  // 自定义验证逻辑
  if (config.customField !== clientInfo.expectedCustomValue) {
    return { valid: false, reason: "custom_validation_failed" };
  }

  return { valid: true };
}
```

## 3. 关键特性

1. **灵活的验证配置**：每个许可证可以有不同的验证规则
2. **本地验证**：验证逻辑在客户端执行，减少服务器负载
3. **批量创建**：支持使用相同配置批量生成许可证
4. **设备绑定**：首次验证时激活许可证，后续验证检查设备信息

## 4. 数据流向

```
创建许可证 → 存储verifyInstance配置 → 客户端请求验证 → 返回配置 → 客户端本地验证
```

这种设计确保了验证逻辑的灵活性和性能，同时保持了服务端的简洁性。
