import { PrismaClient } from '../generated/prisma';
import { PrismaD1 } from '@prisma/adapter-d1';
import { generateLicenseKey } from '../utils/crypto';
import { LicenseCreateRequest, LicenseInfo, ProductInfo, PaginationParams } from '../types/api';

export class LicenseService {
  private prisma: PrismaClient;

  constructor(db: D1Database) {
    const adapter = new PrismaD1(db);
    this.prisma = new PrismaClient({ adapter });
  }

  /**
   * 生成许可证
   */
  async createLicenses(request: LicenseCreateRequest) {
    const { versionId, distributorId, quantity } = request;

    // 获取版本信息
    const version = await this.prisma.productVersion.findUnique({
      where: { id: versionId },
      include: {
        product: true
      }
    });

    if (!version) {
      throw new Error('产品版本不存在');
    }

    // 批量生成许可证
    const licenses: LicenseInfo[] = [];
    for (let i = 0; i < quantity; i++) {
      const licenseKey = generateLicenseKey();
      
      const license = await this.prisma.license.create({
        data: {
          versionId,
          distributorId,
          licenseKey,
          status: 'INACTIVE'
        }
      });
      
      licenses.push({
        id: license.id,
        licenseKey: license.licenseKey,
        status: license.status as 'INACTIVE' | 'ACTIVE' | 'REVOKED',
        createdAt: license.createdAt.toISOString()
      });
    }

    const productInfo: ProductInfo = {
      id: version.product.id,
      name: version.product.name,
      version: version.version,
      versionName: version.versionName || undefined,
      description: version.description || undefined
    };

    return {
      licenses,
      product: productInfo,
      total: quantity
    };
  }

  /**
   * 获取许可证列表
   */
  async getLicenses(params: {
    distributorId?: number;
    versionId?: number;
    status?: string;
    licenseKey?: string;
    pagination: PaginationParams;
  }) {
    const { distributorId, versionId, status, licenseKey, pagination } = params;
    const { page, limit } = pagination;
    const skip = (page - 1) * limit;

    const where: any = {};
    if (distributorId) where.distributorId = distributorId;
    if (versionId) where.versionId = versionId;
    if (status) where.status = status;
    if (licenseKey) where.licenseKey = { contains: licenseKey };

    const [licenses, total] = await Promise.all([
      this.prisma.license.findMany({
        where,
        include: {
          version: {
            include: {
              product: true
            }
          },
          distributor: true
        },
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }),
      this.prisma.license.count({ where })
    ]);

    return {
      licenses: licenses.map(license => ({
        id: license.id,
        licenseKey: license.licenseKey,
        status: license.status as 'INACTIVE' | 'ACTIVE' | 'REVOKED',
        activatedAt: license.activatedAt?.toISOString(),
        createdAt: license.createdAt.toISOString(),
        product: {
          id: license.version.product.id,
          name: license.version.product.name,
          version: license.version.version,
          versionName: license.version.versionName || undefined
        },
        distributor: license.distributor.nickName || license.distributor.username
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * 撤销许可证
   */
  async revokeLicense(licenseId: number) {
    const license = await this.prisma.license.findUnique({
      where: { id: licenseId }
    });

    if (!license) {
      throw new Error('许可证不存在');
    }

    if (license.status === 'REVOKED') {
      throw new Error('许可证已被撤销');
    }

    await this.prisma.license.update({
      where: { id: licenseId },
      data: { status: 'REVOKED' }
    });
  }
}