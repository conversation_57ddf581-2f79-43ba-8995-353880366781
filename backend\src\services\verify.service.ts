import { PrismaClient } from '../generated/prisma';
import { PrismaD1 } from '@prisma/adapter-d1';
import { validateLicenseKeyFormat } from '../utils/crypto';
import { VerifyRequest, VerifyResponse, ProductInfo } from '../types/api';

export class VerifyService {
  private prisma: PrismaClient;

  constructor(db: D1Database) {
    const adapter = new PrismaD1(db);
    this.prisma = new PrismaClient({ adapter });
  }

  /**
   * 验证许可证并获取配置
   */
  async verifyLicense(request: VerifyRequest): Promise<VerifyResponse> {
    const { licenseKey, verifyInstance } = request;

    // 验证许可证密钥格式
    if (!validateLicenseKeyFormat(licenseKey)) {
      return {
        valid: false,
        reason: 'INVALID_FORMAT',
        message: '无效的许可证密钥格式'
      };
    }

    // 查找许可证
    const license = await this.prisma.license.findUnique({
      where: { licenseKey },
      include: {
        version: {
          include: {
            product: true
          }
        }
      }
    });

    if (!license) {
      return {
        valid: false,
        reason: 'LICENSE_NOT_FOUND',
        message: '许可证不存在'
      };
    }

    // 检查许可证是否已撤销
    if (license.status === 'REVOKED') {
      return {
        valid: false,
        reason: 'LICENSE_REVOKED',
        message: '许可证已被撤销'
      };
    }

    const productInfo: ProductInfo = {
      id: license.version.product.id,
      name: license.version.product.name,
      version: license.version.version,
      versionName: license.version.versionName || undefined,
      description: license.version.description || undefined
    };

    // 如果是首次验证（INACTIVE状态），则激活许可证并记录设备信息
    if (license.status === 'INACTIVE') {
      await this.prisma.license.update({
        where: { id: license.id },
        data: {
          status: 'ACTIVE',
          activatedAt: new Date()
        }
      });

      // 返回许可证中存储的验证配置（而不是从模板生成）
      const clientConfig = license.verifyInstance ? JSON.parse(license.verifyInstance) : {};

      return {
        valid: true,
        productInfo,
        verifyInstance: clientConfig,
        activatedAt: new Date().toISOString(),
        lastFetched: new Date().toISOString(),
        message: '许可证激活成功'
      };
    }

    // 已激活的许可证，直接返回存储的验证配置
    // 注意：verifyInstance现在存储的是验证配置，不是设备信息
    // 设备绑定逻辑需要另外实现（可以通过其他字段或表来管理）

    // 返回许可证中存储的验证配置
    const clientConfig = license.verifyInstance ? JSON.parse(license.verifyInstance) : {};

    return {
      valid: true,
      productInfo,
      verifyInstance: clientConfig,
      activatedAt: license.activatedAt?.toISOString(),
      lastFetched: new Date().toISOString(),
      message: '配置获取成功'
    };
  }

}
