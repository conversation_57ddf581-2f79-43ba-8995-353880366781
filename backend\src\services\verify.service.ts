import { PrismaClient } from '../generated/prisma';
import { PrismaD1 } from '@prisma/adapter-d1';
import { validateLicenseKeyFormat } from '../utils/crypto';
import { VerifyRequest, VerifyResponse, ProductInfo } from '../types/api';

export class VerifyService {
  private prisma: PrismaClient;

  constructor(db: D1Database) {
    const adapter = new PrismaD1(db);
    this.prisma = new PrismaClient({ adapter });
  }

  /**
   * 验证许可证并获取配置
   */
  async verifyLicense(request: VerifyRequest): Promise<VerifyResponse> {
    const { licenseKey, verifyInstance } = request;

    // 验证许可证密钥格式
    if (!validateLicenseKeyFormat(licenseKey)) {
      return {
        valid: false,
        reason: 'INVALID_FORMAT',
        message: '无效的许可证密钥格式'
      };
    }

    // 查找许可证
    const license = await this.prisma.license.findUnique({
      where: { licenseKey },
      include: {
        version: {
          include: {
            product: true
          }
        }
      }
    });

    if (!license) {
      return {
        valid: false,
        reason: 'LICENSE_NOT_FOUND',
        message: '许可证不存在'
      };
    }

    // 检查许可证是否已撤销
    if (license.status === 'REVOKED') {
      return {
        valid: false,
        reason: 'LICENSE_REVOKED',
        message: '许可证已被撤销'
      };
    }

    const productInfo: ProductInfo = {
      id: license.version.product.id,
      name: license.version.product.name,
      version: license.version.version,
      versionName: license.version.versionName || undefined,
      description: license.version.description || undefined
    };

    // 如果是首次验证（INACTIVE状态），则激活许可证并绑定设备
    if (license.status === 'INACTIVE') {
      await this.prisma.license.update({
        where: { id: license.id },
        data: {
          status: 'ACTIVE',
          verifyInstance,
          activatedAt: new Date()
        }
      });

      const clientConfig = this.generateClientConfig(license.version.verifyTemplate);

      return {
        valid: true,
        productInfo,
        verifyInstance: clientConfig,
        activatedAt: new Date().toISOString(),
        lastFetched: new Date().toISOString(),
        message: '许可证激活成功'
      };
    }

    // 已激活的许可证检查设备绑定
    if (license.verifyInstance !== verifyInstance) {
      return {
        valid: false,
        reason: 'DEVICE_MISMATCH',
        message: '设备验证失败，许可证已绑定到其他设备'
      };
    }

    // 解析验证模板，生成客户端配置
    const clientConfig = this.generateClientConfig(license.version.verifyTemplate);

    return {
      valid: true,
      productInfo,
      verifyInstance: clientConfig,
      activatedAt: license.activatedAt?.toISOString(),
      lastFetched: new Date().toISOString(),
      message: '配置获取成功'
    };
  }

  /**
   * 根据验证模板生成客户端配置
   * 这里是实际分发商配置的值，客户端拿到后进行本地验证
   */
  private generateClientConfig(templateJson: string): any {
    try {
      const template = JSON.parse(templateJson);
      const config: any = {};
      
      for (const field of template) {
        // 这里应该是分发商实际配置的值
        // 目前使用默认值，后续需要实现分发商配置存储
        config[field.key] = field.default !== undefined ? field.default : field.value;
      }
      
      return config;
    } catch (error) {
      console.error('解析验证模板失败:', error);
      throw new Error('验证模板格式错误');
    }
  }
}