# 更新后的许可证工作流程

## 概述

根据最新需求，我们已经更新了许可证创建和验证流程：

1. **验证接口简化**：只需要传入 `licenseKey`，`verifyInstance` 为可选参数
2. **加密支持**：请求和响应都支持加密
3. **本地验证**：验证逻辑在客户端本地执行

## 1. 许可证创建流程

### 单个创建
```json
POST /api/licenses
{
  "versionId": 1,
  "distributorId": 2,
  "verifyInstance": {
    "expiration": "2025-12-31",
    "maxDevices": 3,
    "features": ["feature1", "feature2"],
    "customField": "test_value"
  }
}
```

### 批量创建
```json
POST /api/licenses
{
  "versionId": 1,
  "distributorId": 2,
  "quantity": 3,
  "verifyInstance": {
    "expiration": "2025-12-31",
    "maxDevices": 5,
    "features": ["feature1", "feature2", "feature3"],
    "customField": "batch_value"
  }
}
```

## 2. 验证流程

### 步骤 1：客户端发送验证请求
```json
POST /api/verify
{
  "licenseKey": "ABCD-1234-EFGH-5678"
  // verifyInstance 现在是可选的
}
```

### 步骤 2：服务端返回加密响应
```json
{
  "success": true,
  "data": {
    "valid": true,
    "encrypted": true,
    "data": "base64_encoded_encrypted_data",
    "message": "许可证激活成功"
  }
}
```

### 步骤 3：客户端解密响应
客户端收到加密响应后，使用相应的解密算法解密数据：

```javascript
// 解密后的数据结构
{
  "valid": true,
  "productInfo": {
    "id": 1,
    "name": "TestProduct",
    "version": "1.0.0",
    "versionName": "正式版"
  },
  "verifyInstance": {
    "expiration": "2025-12-31",
    "maxDevices": 3,
    "features": ["feature1", "feature2"],
    "customField": "test_value"
  },
  "activatedAt": "2025-08-02T01:07:44.245Z",
  "lastFetched": "2025-08-02T01:07:44.246Z",
  "message": "许可证激活成功"
}
```

### 步骤 4：客户端本地验证
```javascript
function validateLicense(config, clientInfo) {
  // 检查过期时间
  if (new Date() > new Date(config.expiration)) {
    return { valid: false, reason: "expired" };
  }

  // 检查设备数量限制
  if (clientInfo.deviceCount > config.maxDevices) {
    return { valid: false, reason: "device_limit_exceeded" };
  }

  // 检查功能权限
  for (const feature of clientInfo.requestedFeatures) {
    if (!config.features.includes(feature)) {
      return { valid: false, reason: "feature_not_authorized" };
    }
  }

  // 自定义验证逻辑
  if (config.customField !== clientInfo.expectedCustomValue) {
    return { valid: false, reason: "custom_validation_failed" };
  }

  return { valid: true };
}
```

## 3. 关键特性

### 安全性
- **加密传输**：所有验证响应都经过加密
- **本地验证**：敏感验证逻辑在客户端执行，减少网络传输
- **密钥管理**：每个产品版本有独立的加密密钥

### 灵活性
- **可选参数**：`verifyInstance` 现在是可选的
- **自定义配置**：每个许可证可以有不同的验证规则
- **批量操作**：支持批量创建相同配置的许可证

### 性能
- **减少服务器负载**：验证逻辑在客户端执行
- **缓存友好**：客户端可以缓存验证配置
- **实时验证**：无需频繁的服务器请求

## 4. API 端点

### 许可证管理
- `POST /api/licenses` - 创建许可证（单个或批量）
- `GET /api/licenses` - 获取许可证列表
- `POST /api/licenses/:id/revoke` - 撤销许可证

### 验证服务
- `POST /api/verify` - 验证许可证（支持加密）
- `POST /api/verify/decrypt` - 解密验证请求（可选）

## 5. 数据流向

```
创建许可证 → 存储verifyInstance配置 → 客户端请求验证 → 
返回加密配置 → 客户端解密 → 本地验证
```

## 6. 模拟测试结果

根据最新的模拟测试，整个工作流程运行正常：

1. ✅ 单个许可证创建成功
2. ✅ 批量许可证创建成功  
3. ✅ 验证请求处理成功
4. ✅ 响应加密/解密成功
5. ✅ 本地验证逻辑正确
6. ✅ 验证失败场景处理正确

这种设计确保了系统的安全性、性能和灵活性，同时简化了客户端的使用方式。
