// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
  previewFeatures = ["driverAdapters"]
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id           Int        @id @default(autoincrement())
  username     String     @unique
  passwordHash String
  role         UserRole
  status       UserStatus @default(ACTIVE)

  // 分发商基本信息
  nickName String?
  wechat   String?
  avatar   String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  licenses       License[]
  authorizations Authorization[]

  @@index([role])
  @@index([status])
  @@index([createdAt])
  @@index([role, status])
  @@map("users")
}

// 产品表（基础信息）
model Product {
  id          Int           @id @default(autoincrement())
  name        String        @unique
  description String?
  category    String?
  status      ProductStatus @default(ACTIVE)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  versions ProductVersion[]

  @@index([status])
  @@index([category])
  @@index([createdAt])
  @@map("products")
}

// 产品版本表（包含默认验证模板和加密密钥）
model ProductVersion {
  id             Int           @id @default(autoincrement())
  productId      Int
  version        String
  versionName    String?
  description    String?
  verifyTemplate String // JSON格式
  encryptionKey  String
  defaultPrice   Float
  downloadLink   String?
  coverUrl       String?
  changelog      String?
  status         ProductStatus @default(ACTIVE)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  product        Product         @relation(fields: [productId], references: [id], onDelete: Cascade)
  licenses       License[]
  authorizations Authorization[]

  @@unique([productId, version])
  @@index([productId])
  @@index([status])
  @@index([createdAt])
  @@index([productId, status])
  @@map("product_versions")
}

// 分发商授权表（店铺商品授权）
model Authorization {
  id            Int                 @id @default(autoincrement())
  distributorId Int
  versionId     Int
  customPrice   Float?
  status        AuthorizationStatus @default(ACTIVE)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  distributor User           @relation(fields: [distributorId], references: [id], onDelete: Cascade)
  version     ProductVersion @relation(fields: [versionId], references: [id], onDelete: Cascade)

  @@unique([distributorId, versionId])
  @@index([distributorId])
  @@index([versionId])
  @@index([status])
  @@index([createdAt])
  @@index([distributorId, status])
  @@map("authorizations")
}

// 许可证表
model License {
  id             Int           @id @default(autoincrement())
  versionId      Int
  licenseKey     String        @unique
  status         LicenseStatus @default(INACTIVE)
  verifyInstance String?
  activatedAt    DateTime?
  distributorId  Int

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关系
  version     ProductVersion @relation(fields: [versionId], references: [id], onDelete: Cascade)
  distributor User           @relation(fields: [distributorId], references: [id], onDelete: Restrict)

  @@index([licenseKey])
  @@index([versionId])
  @@index([distributorId])
  @@index([status])
  @@index([activatedAt])
  @@index([createdAt])
  @@index([distributorId, status])
  @@index([status, createdAt])
  @@map("licenses")
}

// 枚举类型定义
enum UserRole {
  ADMIN
  DISTRIBUTOR
}

enum UserStatus {
  ACTIVE
  INACTIVE
}

enum ProductStatus {
  ACTIVE
  INACTIVE
}

enum AuthorizationStatus {
  ACTIVE
  INACTIVE
}

enum LicenseStatus {
  INACTIVE
  ACTIVE
  REVOKED
}
