import { Hono } from 'hono';
import { LicenseService } from '../services/license.service';
import { createSuccessResponse, createErrorResponse } from '../utils/response';
import { Env, LicenseCreateRequest } from '../types/api';

export const licenseRoutes = new Hono<{ Bindings: Env }>();

/**
 * 生成许可证（支持单个和批量创建）
 * POST /api/licenses
 */
licenseRoutes.post('/licenses', async (c) => {
  try {
    const request: LicenseCreateRequest = await c.req.json();

    // 参数验证
    if (!request.versionId || !request.distributorId || !request.verifyInstance) {
      return c.json(createErrorResponse('INVALID_PARAMS', '参数错误：缺少必要参数'), 400);
    }

    const quantity = request.quantity || 1;
    if (quantity <= 0) {
      return c.json(createErrorResponse('INVALID_PARAMS', '数量必须大于0'), 400);
    }

    const licenseService = new LicenseService(c.env.DB);
    const result = await licenseService.createLicenses(request);

    return c.json(createSuccessResponse(result, `成功生成 ${quantity} 个许可证`), 201);

  } catch (error) {
    console.error('生成许可证失败:', error);
    const message = error instanceof Error ? error.message : '服务器内部错误';
    return c.json(createErrorResponse('INTERNAL_ERROR', message), 500);
  }
});

/**
 * 获取许可证列表
 * GET /api/licenses
 */
licenseRoutes.get('/licenses', async (c) => {
  try {
    const query = c.req.query();
    const distributorId = query.distributorId ? parseInt(query.distributorId) : undefined;
    const versionId = query.versionId ? parseInt(query.versionId) : undefined;
    const { status, licenseKey } = query;
    const page = parseInt(query.page || '1');
    const limit = parseInt(query.limit || '20');

    const licenseService = new LicenseService(c.env.DB);
    const result = await licenseService.getLicenses({
      distributorId,
      versionId,
      status,
      licenseKey,
      pagination: { page, limit }
    });

    return c.json(createSuccessResponse(result));

  } catch (error) {
    console.error('获取许可证列表失败:', error);
    const message = error instanceof Error ? error.message : '服务器内部错误';
    return c.json(createErrorResponse('INTERNAL_ERROR', message), 500);
  }
});

/**
 * 撤销许可证
 * POST /api/licenses/:id/revoke
 */
licenseRoutes.post('/licenses/:id/revoke', async (c) => {
  try {
    const licenseId = parseInt(c.req.param('id'));

    const licenseService = new LicenseService(c.env.DB);
    await licenseService.revokeLicense(licenseId);

    return c.json(createSuccessResponse(null, '许可证撤销成功'));

  } catch (error) {
    console.error('撤销许可证失败:', error);
    const message = error instanceof Error ? error.message : '服务器内部错误';
    return c.json(createErrorResponse('INTERNAL_ERROR', message), 500);
  }
});
