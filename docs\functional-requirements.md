# 功能需求文档

## 项目概述

软件许可证网络验证服务系统，为个人开发者提供软件授权管理和在线验证服务。

## 核心功能模块

### 1. 用户管理模块

#### 1.1 用户角色

- **管理员 (ADMIN)**

  - 系统全权管理
  - 用户管理
  - 产品和版本管理
  - 授权管理
  - 许可证管理
  - 收入统计
    - 各分发商的销售数据
    - 总计预估收入/收入趋势
    - 产品销售排行

- **分发商 (DISTRIBUTOR)**
  - 查看已授权产品
  - 管理自己的许可证
  - 查看销售数据
  - 个人信息管理
  - 数据统计查看
  - 许可证管理
    - 生成许可证
    - 激活许可证
    - 撤销许可证
  - 收入统计
    - 个人销售数据
    - 销售趋势
    - 产品销售排行

#### 1.2 用户功能

- 用户注册/登录
- 密码修改
- 个人信息管理（昵称、微信、头像）
- 用户状态管理（激活/禁用）

### 2. 产品管理模块

#### 2.1 产品基础管理

- 产品创建/编辑/删除
- 产品分类管理
- 产品状态管理（上架/下架）
- 产品信息维护（名称、描述、分类）

#### 2.2 版本管理

- 版本创建/编辑/删除
- 版本信息管理
  - 版本号（如：1.0.0）
  - 版本名称（如：正式版）
  - 版本描述
  - 更新日志
  - 下载链接
  - 封面图片
- 验证模板配置（JSON 格式）
  ```json
  [
    {
      "key": "expiration",
      "type": "date",
      "value": "2024-12-31",
      "default": "2024-12-31"
    },
    {
      "key": "features",
      "type": "array",
      "options": [
        {
          "value": "feature1",
          "label": "特性1"
        },
        {
          "value": "feature2",
          "label": "特性2"
        }
      ],
      "default": ["featre1"]
    },
    {
      "key": "max_devices",
      "type": "number",
      "value": 10,
      "default": 10
    },
    {
      "key": "current_devices",
      "type": "array",
      "value": ["device1", "device2"],
      "default": [],
      "options": [
        {
          "value": "device1",
          "label": "设备1"
        },
        {
          "value": "device2",
          "label": "设备2"
        }
      ]
    }
    {
      "key": "custom_field",
      "type": "string",
      "value": "custom value",
      "default": ""
    }
  ]
  ```
- 加密密钥管理
- 默认价格设置

### 3. 授权管理模块

#### 3.1 分发商授权

- 为分发商授权特定产品版本
- 设置自定义价格（可选）
- 授权状态管理（激活/禁用）
- 授权关系查看

#### 3.2 授权策略

- 一个分发商可以被授权多个产品版本
- 一个产品版本可以授权给多个分发商
- 支持自定义价格覆盖默认价格
- 授权可以随时撤销

### 4. 许可证管理模块

#### 4.1 许可证生成

- 批量生成许可证
- 自动生成唯一许可证密钥
- 关联产品版本和分发商
- 初始状态为未激活

#### 4.2 许可证状态管理

- **未激活 (INACTIVE)**: 新生成的许可证
- **已激活 (ACTIVE)**: 用户已激活使用
- **已撤销 (REVOKED)**: 管理员撤销的许可证

#### 4.3 许可证操作

- 许可证激活（绑定验证实例）
- 许可证撤销
- 许可证查询和筛选
- 激活时间记录

### 5. 许可证验证模块

#### 5.1 在线验证

- 许可证密钥验证
- 验证实例绑定检查
- 产品信息验证
- 返回验证结果和产品信息

#### 5.2 验证策略

- 支持一次性激活绑定
- 验证实例唯一性检查
- 支持自定义验证模板
- 验证日志记录

### 6. 数据统计模块

#### 6.1 管理员统计

- 用户数量统计
- 产品和版本统计
- 许可证状态分布
- 授权关系统计
- 验证次数统计

#### 6.2 分发商统计

- 个人许可证统计
- 销售数据统计
- 激活率统计

## 业务流程

### 1. 产品发布流程

1. 管理员创建产品
2. 创建产品版本
3. 配置验证模板和价格
4. 产品上架

### 2. 分发商授权流程

1. 管理员为分发商授权产品版本
2. 可选设置自定义价格
3. 授权生效，分发商可见该产品

### 3. 许可证销售流程

1. 分发商请求生成许可证
2. 系统批量生成许可证
3. 分发商获得许可证密钥
4. 分发商销售给最终用户

### 4. 许可证激活流程

1. 最终用户获得许可证密钥
2. 软件调用验证接口
3. 系统验证许可证有效性
4. 绑定验证实例（如机器指纹）
5. 返回验证结果

### 5. 许可证验证流程

1. 软件启动时调用验证接口
2. 提交许可证密钥和验证实例
3. 系统检查许可证状态和绑定关系
4. 返回验证结果

## 数据安全要求

### 1. 密码安全

- 用户密码必须加密存储
- 支持密码强度验证
- 支持密码重置功能

### 2. 许可证安全

- 许可证密钥唯一性保证
- 加密密钥安全存储
- 验证实例绑定防止共享

### 3. 接口安全

- JWT Token 认证
- 接口权限控制
- 请求频率限制

## 性能要求

### 1. 响应时间

- 验证接口响应时间 < 500ms
- 管理接口响应时间 < 2s
- 查询接口支持分页

### 2. 并发处理

- 支持高并发验证请求
- 数据库连接池管理
- 缓存机制优化

### 3. 数据存储

- 支持大量许可证数据存储
- 数据库索引优化
- 定期数据清理机制

## 兼容性要求

### 1. 浏览器兼容

- 支持主流现代浏览器
- 响应式设计适配移动端

### 2. API 兼容

- RESTful API 标准
- JSON 数据格式
- 版本化 API 设计

### 3. 部署兼容

- 支持云平台部署
- 支持容器化部署
- 支持负载均衡
