import { Hono } from 'hono';
import { VerifyService } from '../services/verify.service';
import { createSuccessResponse, createErrorResponse } from '../utils/response';
import { Env, VerifyRequest } from '../types/api';

export const verifyRoutes = new Hono<{ Bindings: Env }>();

/**
 * 验证许可证（公开接口，支持加密）
 * POST /api/verify
 */
verifyRoutes.post('/verify', async (c) => {
  try {
    const request: VerifyRequest = await c.req.json();

    // 参数验证
    if (!request.licenseKey) {
      return c.json(createErrorResponse('INVALID_PARAMS', '参数错误：缺少licenseKey'), 400);
    }

    const verifyService = new VerifyService(c.env.DB);
    const result = await verifyService.verifyLicense(request);

    // 如果验证失败，返回错误信息
    if (!result.valid) {
      return c.json(createErrorResponse(result.reason || 'VERIFICATION_FAILED', result.message), 400);
    }

    return c.json(createSuccessResponse(result, result.message));

  } catch (error) {
    console.error('许可证验证失败:', error);
    const message = error instanceof Error ? error.message : '服务器内部错误';
    return c.json(createErrorResponse('INTERNAL_ERROR', message), 500);
  }
});

/**
 * 解密验证请求（如果客户端发送加密数据）
 * POST /api/verify/decrypt
 */
verifyRoutes.post('/verify/decrypt', async (c) => {
  try {
    const { encryptedData, licenseKey } = await c.req.json();

    if (!encryptedData || !licenseKey) {
      return c.json(createErrorResponse('INVALID_PARAMS', '参数错误：缺少加密数据或许可证密钥'), 400);
    }

    const verifyService = new VerifyService(c.env.DB);
    
    // 首先需要获取许可证信息以获取加密密钥
    // 这里简化处理，实际应用中可能需要更复杂的密钥管理
    const basicVerifyResult = await verifyService.verifyLicense({ licenseKey });
    
    if (!basicVerifyResult.valid) {
      return c.json(createErrorResponse('INVALID_LICENSE', '无效的许可证'), 400);
    }

    // 解密请求数据
    // 注意：这里需要从许可证获取加密密钥，实际实现可能需要调整
    const decryptedRequest = await verifyService.decryptRequest(encryptedData, 'encryption_key');
    
    // 处理解密后的请求
    const result = await verifyService.verifyLicense(decryptedRequest);

    return c.json(createSuccessResponse(result, result.message));

  } catch (error) {
    console.error('解密验证失败:', error);
    const message = error instanceof Error ? error.message : '解密或验证失败';
    return c.json(createErrorResponse('DECRYPT_ERROR', message), 500);
  }
});
