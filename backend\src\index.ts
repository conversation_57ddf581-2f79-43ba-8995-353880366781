import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { licenseRoutes } from './routes/license';
import { verifyRoutes } from './routes/verify';
import { Env } from './types/api';

const app = new Hono<{ Bindings: Env }>();

// CORS中间件
app.use('*', cors({
  origin: ['http://localhost:5173', 'https://verify.example.com'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));

// 健康检查
app.get('/', (c) => {
  return c.json({
    message: 'License Verification Service API',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// 注册路由
app.route('/api', licenseRoutes);
app.route('/api', verifyRoutes);

export default app;
