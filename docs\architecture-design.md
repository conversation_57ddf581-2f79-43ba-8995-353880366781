# 架构设计文档

## 系统概述

软件许可证验证系统采用现代化微服务架构，基于 Cloudflare Workers 无服务器平台，提供高性能、高可用、实时响应的许可证验证服务。系统设计注重数据实时性，采用无缓存架构确保所有数据查询都能获得最新结果。

## 技术选型

### 1. 后端技术栈

- **运行环境**: Cloudflare Workers
- **Web 框架**: Hono.js (轻量级、高性能)
- **数据库**: Cloudflare D1 (SQLite 兼容)
- **ORM**: Prisma (类型安全、开发效率高)
- **语言**: TypeScript (类型安全、开发体验好)

### 2. 前端技术栈

- **框架**: React 19 + Vite (快速开发构建)
- **路由**: React Router DOM (客户端路由)
- **UI 组件**: shadcn/ui (现代化组件库)


### 3. 开发工具链

- **包管理**: pnpm (快速、节省空间)
- **构建工具**: Vite (快速构建)
- **测试框架**: Vitest (快速单元测试)
- **代码质量**: ESLint + Prettier + TypeScript

### 4. 部署平台

- **后端**: Cloudflare Workers (全球边缘计算)
- **前端**: Cloudflare Pages (静态站点托管、CDN 加速)
- **数据库**: Cloudflare D1 (全球分布式)
- **静态资源**: Cloudflare R2 (对象存储)

## 系统架构

### 1. 整体架构图

```text
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   管理后台      │    │   客户端SDK     │    │   最终用户软件   │
│ (React + Vite)  │    │  (多语言支持)    │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ HTTPS/REST API        │ HTTPS/REST API        │ HTTPS/REST API
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  API Gateway    │
                    │ (Cloudflare)    │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Workers API    │
                    │   (Hono.js)     │
                    └─────────────────┘
                                 │
                ┌────────────────┼────────────────┐
                │                                 │
       ┌─────────────────┐                     ┌─────────────────┐
       │   D1 Database   │                     │   R2 Storage    │
       │   (SQLite)      │                     │   (Files)       │
       └─────────────────┘                     └─────────────────┘
```

### 2. 分层架构

#### 2.1 表现层 (Presentation Layer)

- **管理后台**: React + Vite + Radix UI

  - 用户管理界面
  - 产品管理界面
  - 授权管理界面
  - 许可证管理界面
  - 数据统计界面

- **客户端 SDK**: 多语言支持
  - JavaScript/TypeScript SDK
  - Python SDK
  - C# SDK
  - Java SDK

#### 2.2 业务逻辑层 (Business Logic Layer)

- **用户服务**: 用户认证、权限管理
- **产品服务**: 产品和版本管理
- **授权服务**: 分发商授权管理
- **许可证服务**: 许可证生成、激活、验证
- **统计服务**: 数据统计和报表

#### 2.3 数据访问层 (Data Access Layer)

- **Prisma ORM**: 类型安全的数据库操作
- **数据库连接池**: 优化数据库连接
- **实时数据层**: 直接访问数据库，确保数据实时性

#### 2.4 基础设施层 (Infrastructure Layer)

- **Cloudflare Workers**: 无服务器计算平台
- **Cloudflare D1**: 分布式 SQLite 数据库
- **Cloudflare R2**: 对象存储服务

### 3. 数据库设计

#### 3.1 核心实体关系

```text
User (用户)
├── role: ADMIN | DISTRIBUTOR
├── status: ACTIVE | INACTIVE
└── 关联: licenses[], authorizations[]

Product (产品)
├── status: ACTIVE | INACTIVE
└── 关联: versions[]

ProductVersion (产品版本)
├── verifyTemplate: JSON配置
├── encryptionKey: 加密密钥
└── 关联: product, licenses[], authorizations[]

Authorization (授权)
├── status: ACTIVE | INACTIVE
└── 关联: distributor(User), version(ProductVersion)

License (许可证)
├── status: INACTIVE | ACTIVE | REVOKED
├── verifyInstance: 验证实例绑定
└── 关联: version(ProductVersion), distributor(User)
```

#### 3.2 索引策略

- 主键索引: 所有表的 id 字段
- 唯一索引: username, licenseKey
- 复合索引: (role, status), (distributorId, status)
- 时间索引: createdAt, updatedAt, activatedAt

### 4. 安全架构

#### 4.1 认证授权

- **JWT Token**: 无状态认证
- **角色权限**: RBAC 权限模型
- **Token 刷新**: 自动续期机制
- **会话管理**: 安全会话控制

#### 4.2 数据安全

- **密码加密**: bcrypt 哈希算法
- **传输加密**: HTTPS/TLS 1.3
- **数据加密**: 敏感数据字段加密
- **访问控制**: 细粒度权限控制

#### 4.3 接口安全

- **请求限流**: 防止 API 滥用
- **参数验证**: 严格的输入验证
- **CORS 配置**: 跨域请求控制
- **安全头**: 安全 HTTP 头设置

### 5. 实时性设计

#### 5.1 实时性优化策略

- **无缓存设计**: 系统注重实时性，不使用数据缓存机制
- **直连数据库**: 所有数据请求直接访问数据库，确保数据实时性
- **静态资源缓存**: 仅对不变的静态资源（CSS、JS、图片）进行CDN缓存
- **禁用浏览器缓存**: API响应设置 `Cache-Control: no-cache, no-store` 头
- **实时数据同步**: 所有数据变更立即生效，无延迟
- **最小化中间层**: 减少数据传输链路，直达数据源