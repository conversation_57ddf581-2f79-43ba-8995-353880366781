var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __esm = (fn, res) => function __init() {
  return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
};
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __export = (target, all) => {
  for (var name2 in all)
    __defProp(target, name2, { get: all[name2], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));

// wrangler-modules-watch:wrangler:modules-watch
var init_wrangler_modules_watch = __esm({
  "wrangler-modules-watch:wrangler:modules-watch"() {
    init_modules_watch_stub();
  }
});

// node_modules/.pnpm/wrangler@4.27.0/node_modules/wrangler/templates/modules-watch-stub.js
var init_modules_watch_stub = __esm({
  "node_modules/.pnpm/wrangler@4.27.0/node_modules/wrangler/templates/modules-watch-stub.js"() {
    init_wrangler_modules_watch();
  }
});

// src/generated/prisma/runtime/wasm-engine-edge.js
var require_wasm_engine_edge = __commonJS({
  "src/generated/prisma/runtime/wasm-engine-edge.js"(exports, module) {
    "use strict";
    init_modules_watch_stub();
    var Xo = Object.create;
    var It = Object.defineProperty;
    var Zo = Object.getOwnPropertyDescriptor;
    var es = Object.getOwnPropertyNames;
    var ts = Object.getPrototypeOf;
    var rs = Object.prototype.hasOwnProperty;
    var ie = /* @__PURE__ */ __name((t, e) => () => (t && (e = t(t = 0)), e), "ie");
    var Fe = /* @__PURE__ */ __name((t, e) => () => (e || t((e = { exports: {} }).exports, e), e.exports), "Fe");
    var it = /* @__PURE__ */ __name((t, e) => {
      for (var r in e) It(t, r, { get: e[r], enumerable: true });
    }, "it");
    var pn = /* @__PURE__ */ __name((t, e, r, n) => {
      if (e && typeof e == "object" || typeof e == "function") for (let i of es(e)) !rs.call(t, i) && i !== r && It(t, i, { get: /* @__PURE__ */ __name(() => e[i], "get"), enumerable: !(n = Zo(e, i)) || n.enumerable });
      return t;
    }, "pn");
    var ot = /* @__PURE__ */ __name((t, e, r) => (r = t != null ? Xo(ts(t)) : {}, pn(e || !t || !t.__esModule ? It(r, "default", { value: t, enumerable: true }) : r, t)), "ot");
    var ns = /* @__PURE__ */ __name((t) => pn(It({}, "__esModule", { value: true }), t), "ns");
    function Pr(t, e) {
      if (e = e.toLowerCase(), e === "utf8" || e === "utf-8") return new h(as.encode(t));
      if (e === "base64" || e === "base64url") return t = t.replace(/-/g, "+").replace(/_/g, "/"), t = t.replace(/[^A-Za-z0-9+/]/g, ""), new h([...atob(t)].map((r) => r.charCodeAt(0)));
      if (e === "binary" || e === "ascii" || e === "latin1" || e === "latin-1") return new h([...t].map((r) => r.charCodeAt(0)));
      if (e === "ucs2" || e === "ucs-2" || e === "utf16le" || e === "utf-16le") {
        let r = new h(t.length * 2), n = new DataView(r.buffer);
        for (let i = 0; i < t.length; i++) n.setUint16(i * 2, t.charCodeAt(i), true);
        return r;
      }
      if (e === "hex") {
        let r = new h(t.length / 2);
        for (let n = 0, i = 0; i < t.length; i += 2, n++) r[n] = parseInt(t.slice(i, i + 2), 16);
        return r;
      }
      fn(`encoding "${e}"`);
    }
    __name(Pr, "Pr");
    function is(t) {
      let r = Object.getOwnPropertyNames(DataView.prototype).filter((a) => a.startsWith("get") || a.startsWith("set")), n = r.map((a) => a.replace("get", "read").replace("set", "write")), i = /* @__PURE__ */ __name((a, f) => function(y = 0) {
        return $3(y, "offset"), X(y, "offset"), j(y, "offset", this.length - 1), new DataView(this.buffer)[r[a]](y, f);
      }, "i"), o = /* @__PURE__ */ __name((a, f) => function(y, C = 0) {
        let R = r[a].match(/set(\w+\d+)/)[1].toLowerCase(), O = ss[R];
        return $3(C, "offset"), X(C, "offset"), j(C, "offset", this.length - 1), os(y, "value", O[0], O[1]), new DataView(this.buffer)[r[a]](C, y, f), C + parseInt(r[a].match(/\d+/)[0]) / 8;
      }, "o"), s = /* @__PURE__ */ __name((a) => {
        a.forEach((f) => {
          f.includes("Uint") && (t[f.replace("Uint", "UInt")] = t[f]), f.includes("Float64") && (t[f.replace("Float64", "Double")] = t[f]), f.includes("Float32") && (t[f.replace("Float32", "Float")] = t[f]);
        });
      }, "s");
      n.forEach((a, f) => {
        a.startsWith("read") && (t[a] = i(f, false), t[a + "LE"] = i(f, true), t[a + "BE"] = i(f, false)), a.startsWith("write") && (t[a] = o(f, false), t[a + "LE"] = o(f, true), t[a + "BE"] = o(f, false)), s([a, a + "LE", a + "BE"]);
      });
    }
    __name(is, "is");
    function fn(t) {
      throw new Error(`Buffer polyfill does not implement "${t}"`);
    }
    __name(fn, "fn");
    function Mt(t, e) {
      if (!(t instanceof Uint8Array)) throw new TypeError(`The "${e}" argument must be an instance of Buffer or Uint8Array`);
    }
    __name(Mt, "Mt");
    function j(t, e, r = cs + 1) {
      if (t < 0 || t > r) {
        let n = new RangeError(`The value of "${e}" is out of range. It must be >= 0 && <= ${r}. Received ${t}`);
        throw n.code = "ERR_OUT_OF_RANGE", n;
      }
    }
    __name(j, "j");
    function $3(t, e) {
      if (typeof t != "number") {
        let r = new TypeError(`The "${e}" argument must be of type number. Received type ${typeof t}.`);
        throw r.code = "ERR_INVALID_ARG_TYPE", r;
      }
    }
    __name($3, "$");
    function X(t, e) {
      if (!Number.isInteger(t) || Number.isNaN(t)) {
        let r = new RangeError(`The value of "${e}" is out of range. It must be an integer. Received ${t}`);
        throw r.code = "ERR_OUT_OF_RANGE", r;
      }
    }
    __name(X, "X");
    function os(t, e, r, n) {
      if (t < r || t > n) {
        let i = new RangeError(`The value of "${e}" is out of range. It must be >= ${r} and <= ${n}. Received ${t}`);
        throw i.code = "ERR_OUT_OF_RANGE", i;
      }
    }
    __name(os, "os");
    function dn(t, e) {
      if (typeof t != "string") {
        let r = new TypeError(`The "${e}" argument must be of type string. Received type ${typeof t}`);
        throw r.code = "ERR_INVALID_ARG_TYPE", r;
      }
    }
    __name(dn, "dn");
    function ms(t, e = "utf8") {
      return h.from(t, e);
    }
    __name(ms, "ms");
    var h;
    var ss;
    var as;
    var ls;
    var us;
    var cs;
    var b;
    var vr;
    var u = ie(() => {
      "use strict";
      h = class t extends Uint8Array {
        static {
          __name(this, "t");
        }
        _isBuffer = true;
        get offset() {
          return this.byteOffset;
        }
        static alloc(e, r = 0, n = "utf8") {
          return dn(n, "encoding"), t.allocUnsafe(e).fill(r, n);
        }
        static allocUnsafe(e) {
          return t.from(e);
        }
        static allocUnsafeSlow(e) {
          return t.from(e);
        }
        static isBuffer(e) {
          return e && !!e._isBuffer;
        }
        static byteLength(e, r = "utf8") {
          if (typeof e == "string") return Pr(e, r).byteLength;
          if (e && e.byteLength) return e.byteLength;
          let n = new TypeError('The "string" argument must be of type string or an instance of Buffer or ArrayBuffer.');
          throw n.code = "ERR_INVALID_ARG_TYPE", n;
        }
        static isEncoding(e) {
          return us.includes(e);
        }
        static compare(e, r) {
          Mt(e, "buff1"), Mt(r, "buff2");
          for (let n = 0; n < e.length; n++) {
            if (e[n] < r[n]) return -1;
            if (e[n] > r[n]) return 1;
          }
          return e.length === r.length ? 0 : e.length > r.length ? 1 : -1;
        }
        static from(e, r = "utf8") {
          if (e && typeof e == "object" && e.type === "Buffer") return new t(e.data);
          if (typeof e == "number") return new t(new Uint8Array(e));
          if (typeof e == "string") return Pr(e, r);
          if (ArrayBuffer.isView(e)) {
            let { byteOffset: n, byteLength: i, buffer: o } = e;
            return "map" in e && typeof e.map == "function" ? new t(e.map((s) => s % 256), n, i) : new t(o, n, i);
          }
          if (e && typeof e == "object" && ("length" in e || "byteLength" in e || "buffer" in e)) return new t(e);
          throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.");
        }
        static concat(e, r) {
          if (e.length === 0) return t.alloc(0);
          let n = [].concat(...e.map((o) => [...o])), i = t.alloc(r !== void 0 ? r : n.length);
          return i.set(r !== void 0 ? n.slice(0, r) : n), i;
        }
        slice(e = 0, r = this.length) {
          return this.subarray(e, r);
        }
        subarray(e = 0, r = this.length) {
          return Object.setPrototypeOf(super.subarray(e, r), t.prototype);
        }
        reverse() {
          return super.reverse(), this;
        }
        readIntBE(e, r) {
          $3(e, "offset"), X(e, "offset"), j(e, "offset", this.length - 1), $3(r, "byteLength"), X(r, "byteLength");
          let n = new DataView(this.buffer, e, r), i = 0;
          for (let o = 0; o < r; o++) i = i * 256 + n.getUint8(o);
          return n.getUint8(0) & 128 && (i -= Math.pow(256, r)), i;
        }
        readIntLE(e, r) {
          $3(e, "offset"), X(e, "offset"), j(e, "offset", this.length - 1), $3(r, "byteLength"), X(r, "byteLength");
          let n = new DataView(this.buffer, e, r), i = 0;
          for (let o = 0; o < r; o++) i += n.getUint8(o) * Math.pow(256, o);
          return n.getUint8(r - 1) & 128 && (i -= Math.pow(256, r)), i;
        }
        readUIntBE(e, r) {
          $3(e, "offset"), X(e, "offset"), j(e, "offset", this.length - 1), $3(r, "byteLength"), X(r, "byteLength");
          let n = new DataView(this.buffer, e, r), i = 0;
          for (let o = 0; o < r; o++) i = i * 256 + n.getUint8(o);
          return i;
        }
        readUintBE(e, r) {
          return this.readUIntBE(e, r);
        }
        readUIntLE(e, r) {
          $3(e, "offset"), X(e, "offset"), j(e, "offset", this.length - 1), $3(r, "byteLength"), X(r, "byteLength");
          let n = new DataView(this.buffer, e, r), i = 0;
          for (let o = 0; o < r; o++) i += n.getUint8(o) * Math.pow(256, o);
          return i;
        }
        readUintLE(e, r) {
          return this.readUIntLE(e, r);
        }
        writeIntBE(e, r, n) {
          return e = e < 0 ? e + Math.pow(256, n) : e, this.writeUIntBE(e, r, n);
        }
        writeIntLE(e, r, n) {
          return e = e < 0 ? e + Math.pow(256, n) : e, this.writeUIntLE(e, r, n);
        }
        writeUIntBE(e, r, n) {
          $3(r, "offset"), X(r, "offset"), j(r, "offset", this.length - 1), $3(n, "byteLength"), X(n, "byteLength");
          let i = new DataView(this.buffer, r, n);
          for (let o = n - 1; o >= 0; o--) i.setUint8(o, e & 255), e = e / 256;
          return r + n;
        }
        writeUintBE(e, r, n) {
          return this.writeUIntBE(e, r, n);
        }
        writeUIntLE(e, r, n) {
          $3(r, "offset"), X(r, "offset"), j(r, "offset", this.length - 1), $3(n, "byteLength"), X(n, "byteLength");
          let i = new DataView(this.buffer, r, n);
          for (let o = 0; o < n; o++) i.setUint8(o, e & 255), e = e / 256;
          return r + n;
        }
        writeUintLE(e, r, n) {
          return this.writeUIntLE(e, r, n);
        }
        toJSON() {
          return { type: "Buffer", data: Array.from(this) };
        }
        swap16() {
          let e = new DataView(this.buffer, this.byteOffset, this.byteLength);
          for (let r = 0; r < this.length; r += 2) e.setUint16(r, e.getUint16(r, true), false);
          return this;
        }
        swap32() {
          let e = new DataView(this.buffer, this.byteOffset, this.byteLength);
          for (let r = 0; r < this.length; r += 4) e.setUint32(r, e.getUint32(r, true), false);
          return this;
        }
        swap64() {
          let e = new DataView(this.buffer, this.byteOffset, this.byteLength);
          for (let r = 0; r < this.length; r += 8) e.setBigUint64(r, e.getBigUint64(r, true), false);
          return this;
        }
        compare(e, r = 0, n = e.length, i = 0, o = this.length) {
          return Mt(e, "target"), $3(r, "targetStart"), $3(n, "targetEnd"), $3(i, "sourceStart"), $3(o, "sourceEnd"), j(r, "targetStart"), j(n, "targetEnd", e.length), j(i, "sourceStart"), j(o, "sourceEnd", this.length), t.compare(this.slice(i, o), e.slice(r, n));
        }
        equals(e) {
          return Mt(e, "otherBuffer"), this.length === e.length && this.every((r, n) => r === e[n]);
        }
        copy(e, r = 0, n = 0, i = this.length) {
          j(r, "targetStart"), j(n, "sourceStart", this.length), j(i, "sourceEnd"), r >>>= 0, n >>>= 0, i >>>= 0;
          let o = 0;
          for (; n < i && !(this[n] === void 0 || e[r] === void 0); ) e[r] = this[n], o++, n++, r++;
          return o;
        }
        write(e, r, n, i = "utf8") {
          let o = typeof r == "string" ? 0 : r ?? 0, s = typeof n == "string" ? this.length - o : n ?? this.length - o;
          return i = typeof r == "string" ? r : typeof n == "string" ? n : i, $3(o, "offset"), $3(s, "length"), j(o, "offset", this.length), j(s, "length", this.length), (i === "ucs2" || i === "ucs-2" || i === "utf16le" || i === "utf-16le") && (s = s - s % 2), Pr(e, i).copy(this, o, 0, s);
        }
        fill(e = 0, r = 0, n = this.length, i = "utf-8") {
          let o = typeof r == "string" ? 0 : r, s = typeof n == "string" ? this.length : n;
          if (i = typeof r == "string" ? r : typeof n == "string" ? n : i, e = t.from(typeof e == "number" ? [e] : e ?? [], i), dn(i, "encoding"), j(o, "offset", this.length), j(s, "end", this.length), e.length !== 0) for (let a = o; a < s; a += e.length) super.set(e.slice(0, e.length + a >= this.length ? this.length - a : e.length), a);
          return this;
        }
        includes(e, r = null, n = "utf-8") {
          return this.indexOf(e, r, n) !== -1;
        }
        lastIndexOf(e, r = null, n = "utf-8") {
          return this.indexOf(e, r, n, true);
        }
        indexOf(e, r = null, n = "utf-8", i = false) {
          let o = i ? this.findLastIndex.bind(this) : this.findIndex.bind(this);
          n = typeof r == "string" ? r : n;
          let s = t.from(typeof e == "number" ? [e] : e, n), a = typeof r == "string" ? 0 : r;
          return a = typeof r == "number" ? a : null, a = Number.isNaN(a) ? null : a, a ??= i ? this.length : 0, a = a < 0 ? this.length + a : a, s.length === 0 && i === false ? a >= this.length ? this.length : a : s.length === 0 && i === true ? (a >= this.length ? this.length : a) || this.length : o((f, y) => (i ? y <= a : y >= a) && this[y] === s[0] && s.every((R, O) => this[y + O] === R));
        }
        toString(e = "utf8", r = 0, n = this.length) {
          if (r = r < 0 ? 0 : r, e = e.toString().toLowerCase(), n <= 0) return "";
          if (e === "utf8" || e === "utf-8") return ls.decode(this.slice(r, n));
          if (e === "base64" || e === "base64url") {
            let i = btoa(this.reduce((o, s) => o + vr(s), ""));
            return e === "base64url" ? i.replace(/\+/g, "-").replace(/\//g, "_").replace(/=+$/, "") : i;
          }
          if (e === "binary" || e === "ascii" || e === "latin1" || e === "latin-1") return this.slice(r, n).reduce((i, o) => i + vr(o & (e === "ascii" ? 127 : 255)), "");
          if (e === "ucs2" || e === "ucs-2" || e === "utf16le" || e === "utf-16le") {
            let i = new DataView(this.buffer.slice(r, n));
            return Array.from({ length: i.byteLength / 2 }, (o, s) => s * 2 + 1 < i.byteLength ? vr(i.getUint16(s * 2, true)) : "").join("");
          }
          if (e === "hex") return this.slice(r, n).reduce((i, o) => i + o.toString(16).padStart(2, "0"), "");
          fn(`encoding "${e}"`);
        }
        toLocaleString() {
          return this.toString();
        }
        inspect() {
          return `<Buffer ${this.toString("hex").match(/.{1,2}/g).join(" ")}>`;
        }
      };
      ss = { int8: [-128, 127], int16: [-32768, 32767], int32: [-2147483648, 2147483647], uint8: [0, 255], uint16: [0, 65535], uint32: [0, 4294967295], float32: [-1 / 0, 1 / 0], float64: [-1 / 0, 1 / 0], bigint64: [-0x8000000000000000n, 0x7fffffffffffffffn], biguint64: [0n, 0xffffffffffffffffn] }, as = new TextEncoder(), ls = new TextDecoder(), us = ["utf8", "utf-8", "hex", "base64", "ascii", "binary", "base64url", "ucs2", "ucs-2", "utf16le", "utf-16le", "latin1", "latin-1"], cs = 4294967295;
      is(h.prototype);
      b = new Proxy(ms, { construct(t, [e, r]) {
        return h.from(e, r);
      }, get(t, e) {
        return h[e];
      } }), vr = String.fromCodePoint;
    });
    var g;
    var x;
    var c = ie(() => {
      "use strict";
      g = { nextTick: /* @__PURE__ */ __name((t, ...e) => {
        setTimeout(() => {
          t(...e);
        }, 0);
      }, "nextTick"), env: {}, version: "", cwd: /* @__PURE__ */ __name(() => "/", "cwd"), stderr: {}, argv: ["/bin/node"], pid: 1e4 }, { cwd: x } = g;
    });
    var P;
    var m = ie(() => {
      "use strict";
      P = globalThis.performance ?? (() => {
        let t = Date.now();
        return { now: /* @__PURE__ */ __name(() => Date.now() - t, "now") };
      })();
    });
    var E;
    var p = ie(() => {
      "use strict";
      E = /* @__PURE__ */ __name(() => {
      }, "E");
      E.prototype = E;
    });
    var w;
    var d = ie(() => {
      "use strict";
      w = class {
        static {
          __name(this, "w");
        }
        value;
        constructor(e) {
          this.value = e;
        }
        deref() {
          return this.value;
        }
      };
    });
    function bn(t, e) {
      var r, n, i, o, s, a, f, y, C = t.constructor, R = C.precision;
      if (!t.s || !e.s) return e.s || (e = new C(t)), q ? L(e, R) : e;
      if (f = t.d, y = e.d, s = t.e, i = e.e, f = f.slice(), o = s - i, o) {
        for (o < 0 ? (n = f, o = -o, a = y.length) : (n = y, i = s, a = f.length), s = Math.ceil(R / N), a = s > a ? s + 1 : a + 1, o > a && (o = a, n.length = 1), n.reverse(); o--; ) n.push(0);
        n.reverse();
      }
      for (a = f.length, o = y.length, a - o < 0 && (o = a, n = y, y = f, f = n), r = 0; o; ) r = (f[--o] = f[o] + y[o] + r) / J | 0, f[o] %= J;
      for (r && (f.unshift(r), ++i), a = f.length; f[--a] == 0; ) f.pop();
      return e.d = f, e.e = i, q ? L(e, R) : e;
    }
    __name(bn, "bn");
    function me(t, e, r) {
      if (t !== ~~t || t < e || t > r) throw Error(Ie + t);
    }
    __name(me, "me");
    function ce(t) {
      var e, r, n, i = t.length - 1, o = "", s = t[0];
      if (i > 0) {
        for (o += s, e = 1; e < i; e++) n = t[e] + "", r = N - n.length, r && (o += ve(r)), o += n;
        s = t[e], n = s + "", r = N - n.length, r && (o += ve(r));
      } else if (s === 0) return "0";
      for (; s % 10 === 0; ) s /= 10;
      return o + s;
    }
    __name(ce, "ce");
    function wn(t, e) {
      var r, n, i, o, s, a, f = 0, y = 0, C = t.constructor, R = C.precision;
      if (V(t) > 16) throw Error(Cr + V(t));
      if (!t.s) return new C(te);
      for (e == null ? (q = false, a = R) : a = e, s = new C(0.03125); t.abs().gte(0.1); ) t = t.times(s), y += 5;
      for (n = Math.log(Oe(2, y)) / Math.LN10 * 2 + 5 | 0, a += n, r = i = o = new C(te), C.precision = a; ; ) {
        if (i = L(i.times(t), a), r = r.times(++f), s = o.plus(be(i, r, a)), ce(s.d).slice(0, a) === ce(o.d).slice(0, a)) {
          for (; y--; ) o = L(o.times(o), a);
          return C.precision = R, e == null ? (q = true, L(o, R)) : o;
        }
        o = s;
      }
    }
    __name(wn, "wn");
    function V(t) {
      for (var e = t.e * N, r = t.d[0]; r >= 10; r /= 10) e++;
      return e;
    }
    __name(V, "V");
    function Tr(t, e, r) {
      if (e > t.LN10.sd()) throw q = true, r && (t.precision = r), Error(oe + "LN10 precision limit exceeded");
      return L(new t(t.LN10), e);
    }
    __name(Tr, "Tr");
    function ve(t) {
      for (var e = ""; t--; ) e += "0";
      return e;
    }
    __name(ve, "ve");
    function st(t, e) {
      var r, n, i, o, s, a, f, y, C, R = 1, O = 10, A = t, I = A.d, k = A.constructor, M = k.precision;
      if (A.s < 1) throw Error(oe + (A.s ? "NaN" : "-Infinity"));
      if (A.eq(te)) return new k(0);
      if (e == null ? (q = false, y = M) : y = e, A.eq(10)) return e == null && (q = true), Tr(k, y);
      if (y += O, k.precision = y, r = ce(I), n = r.charAt(0), o = V(A), Math.abs(o) < 15e14) {
        for (; n < 7 && n != 1 || n == 1 && r.charAt(1) > 3; ) A = A.times(t), r = ce(A.d), n = r.charAt(0), R++;
        o = V(A), n > 1 ? (A = new k("0." + r), o++) : A = new k(n + "." + r.slice(1));
      } else return f = Tr(k, y + 2, M).times(o + ""), A = st(new k(n + "." + r.slice(1)), y - O).plus(f), k.precision = M, e == null ? (q = true, L(A, M)) : A;
      for (a = s = A = be(A.minus(te), A.plus(te), y), C = L(A.times(A), y), i = 3; ; ) {
        if (s = L(s.times(C), y), f = a.plus(be(s, new k(i), y)), ce(f.d).slice(0, y) === ce(a.d).slice(0, y)) return a = a.times(2), o !== 0 && (a = a.plus(Tr(k, y + 2, M).times(o + ""))), a = be(a, new k(R), y), k.precision = M, e == null ? (q = true, L(a, M)) : a;
        a = f, i += 2;
      }
    }
    __name(st, "st");
    function gn(t, e) {
      var r, n, i;
      for ((r = e.indexOf(".")) > -1 && (e = e.replace(".", "")), (n = e.search(/e/i)) > 0 ? (r < 0 && (r = n), r += +e.slice(n + 1), e = e.substring(0, n)) : r < 0 && (r = e.length), n = 0; e.charCodeAt(n) === 48; ) ++n;
      for (i = e.length; e.charCodeAt(i - 1) === 48; ) --i;
      if (e = e.slice(n, i), e) {
        if (i -= n, r = r - n - 1, t.e = Ne(r / N), t.d = [], n = (r + 1) % N, r < 0 && (n += N), n < i) {
          for (n && t.d.push(+e.slice(0, n)), i -= N; n < i; ) t.d.push(+e.slice(n, n += N));
          e = e.slice(n), n = N - e.length;
        } else n -= i;
        for (; n--; ) e += "0";
        if (t.d.push(+e), q && (t.e > Dt || t.e < -Dt)) throw Error(Cr + r);
      } else t.s = 0, t.e = 0, t.d = [0];
      return t;
    }
    __name(gn, "gn");
    function L(t, e, r) {
      var n, i, o, s, a, f, y, C, R = t.d;
      for (s = 1, o = R[0]; o >= 10; o /= 10) s++;
      if (n = e - s, n < 0) n += N, i = e, y = R[C = 0];
      else {
        if (C = Math.ceil((n + 1) / N), o = R.length, C >= o) return t;
        for (y = o = R[C], s = 1; o >= 10; o /= 10) s++;
        n %= N, i = n - N + s;
      }
      if (r !== void 0 && (o = Oe(10, s - i - 1), a = y / o % 10 | 0, f = e < 0 || R[C + 1] !== void 0 || y % o, f = r < 4 ? (a || f) && (r == 0 || r == (t.s < 0 ? 3 : 2)) : a > 5 || a == 5 && (r == 4 || f || r == 6 && (n > 0 ? i > 0 ? y / Oe(10, s - i) : 0 : R[C - 1]) % 10 & 1 || r == (t.s < 0 ? 8 : 7))), e < 1 || !R[0]) return f ? (o = V(t), R.length = 1, e = e - o - 1, R[0] = Oe(10, (N - e % N) % N), t.e = Ne(-e / N) || 0) : (R.length = 1, R[0] = t.e = t.s = 0), t;
      if (n == 0 ? (R.length = C, o = 1, C--) : (R.length = C + 1, o = Oe(10, N - n), R[C] = i > 0 ? (y / Oe(10, s - i) % Oe(10, i) | 0) * o : 0), f) for (; ; ) if (C == 0) {
        (R[0] += o) == J && (R[0] = 1, ++t.e);
        break;
      } else {
        if (R[C] += o, R[C] != J) break;
        R[C--] = 0, o = 1;
      }
      for (n = R.length; R[--n] === 0; ) R.pop();
      if (q && (t.e > Dt || t.e < -Dt)) throw Error(Cr + V(t));
      return t;
    }
    __name(L, "L");
    function En(t, e) {
      var r, n, i, o, s, a, f, y, C, R, O = t.constructor, A = O.precision;
      if (!t.s || !e.s) return e.s ? e.s = -e.s : e = new O(t), q ? L(e, A) : e;
      if (f = t.d, R = e.d, n = e.e, y = t.e, f = f.slice(), s = y - n, s) {
        for (C = s < 0, C ? (r = f, s = -s, a = R.length) : (r = R, n = y, a = f.length), i = Math.max(Math.ceil(A / N), a) + 2, s > i && (s = i, r.length = 1), r.reverse(), i = s; i--; ) r.push(0);
        r.reverse();
      } else {
        for (i = f.length, a = R.length, C = i < a, C && (a = i), i = 0; i < a; i++) if (f[i] != R[i]) {
          C = f[i] < R[i];
          break;
        }
        s = 0;
      }
      for (C && (r = f, f = R, R = r, e.s = -e.s), a = f.length, i = R.length - a; i > 0; --i) f[a++] = 0;
      for (i = R.length; i > s; ) {
        if (f[--i] < R[i]) {
          for (o = i; o && f[--o] === 0; ) f[o] = J - 1;
          --f[o], f[i] += J;
        }
        f[i] -= R[i];
      }
      for (; f[--a] === 0; ) f.pop();
      for (; f[0] === 0; f.shift()) --n;
      return f[0] ? (e.d = f, e.e = n, q ? L(e, A) : e) : new O(0);
    }
    __name(En, "En");
    function Me(t, e, r) {
      var n, i = V(t), o = ce(t.d), s = o.length;
      return e ? (r && (n = r - s) > 0 ? o = o.charAt(0) + "." + o.slice(1) + ve(n) : s > 1 && (o = o.charAt(0) + "." + o.slice(1)), o = o + (i < 0 ? "e" : "e+") + i) : i < 0 ? (o = "0." + ve(-i - 1) + o, r && (n = r - s) > 0 && (o += ve(n))) : i >= s ? (o += ve(i + 1 - s), r && (n = r - i - 1) > 0 && (o = o + "." + ve(n))) : ((n = i + 1) < s && (o = o.slice(0, n) + "." + o.slice(n)), r && (n = r - s) > 0 && (i + 1 === s && (o += "."), o += ve(n))), t.s < 0 ? "-" + o : o;
    }
    __name(Me, "Me");
    function yn(t, e) {
      if (t.length > e) return t.length = e, true;
    }
    __name(yn, "yn");
    function xn(t) {
      var e, r, n;
      function i(o) {
        var s = this;
        if (!(s instanceof i)) return new i(o);
        if (s.constructor = i, o instanceof i) {
          s.s = o.s, s.e = o.e, s.d = (o = o.d) ? o.slice() : o;
          return;
        }
        if (typeof o == "number") {
          if (o * 0 !== 0) throw Error(Ie + o);
          if (o > 0) s.s = 1;
          else if (o < 0) o = -o, s.s = -1;
          else {
            s.s = 0, s.e = 0, s.d = [0];
            return;
          }
          if (o === ~~o && o < 1e7) {
            s.e = 0, s.d = [o];
            return;
          }
          return gn(s, o.toString());
        } else if (typeof o != "string") throw Error(Ie + o);
        if (o.charCodeAt(0) === 45 ? (o = o.slice(1), s.s = -1) : s.s = 1, ds.test(o)) gn(s, o);
        else throw Error(Ie + o);
      }
      __name(i, "i");
      if (i.prototype = S, i.ROUND_UP = 0, i.ROUND_DOWN = 1, i.ROUND_CEIL = 2, i.ROUND_FLOOR = 3, i.ROUND_HALF_UP = 4, i.ROUND_HALF_DOWN = 5, i.ROUND_HALF_EVEN = 6, i.ROUND_HALF_CEIL = 7, i.ROUND_HALF_FLOOR = 8, i.clone = xn, i.config = i.set = fs, t === void 0 && (t = {}), t) for (n = ["precision", "rounding", "toExpNeg", "toExpPos", "LN10"], e = 0; e < n.length; ) t.hasOwnProperty(r = n[e++]) || (t[r] = this[r]);
      return i.config(t), i;
    }
    __name(xn, "xn");
    function fs(t) {
      if (!t || typeof t != "object") throw Error(oe + "Object expected");
      var e, r, n, i = ["precision", 1, Ue, "rounding", 0, 8, "toExpNeg", -1 / 0, 0, "toExpPos", 0, 1 / 0];
      for (e = 0; e < i.length; e += 3) if ((n = t[r = i[e]]) !== void 0) if (Ne(n) === n && n >= i[e + 1] && n <= i[e + 2]) this[r] = n;
      else throw Error(Ie + r + ": " + n);
      if ((n = t[r = "LN10"]) !== void 0) if (n == Math.LN10) this[r] = new this(n);
      else throw Error(Ie + r + ": " + n);
      return this;
    }
    __name(fs, "fs");
    var Ue;
    var ps;
    var Rr;
    var q;
    var oe;
    var Ie;
    var Cr;
    var Ne;
    var Oe;
    var ds;
    var te;
    var J;
    var N;
    var hn;
    var Dt;
    var S;
    var be;
    var Rr;
    var _t;
    var Pn = ie(() => {
      "use strict";
      u();
      c();
      m();
      p();
      d();
      l();
      Ue = 1e9, ps = { precision: 20, rounding: 4, toExpNeg: -7, toExpPos: 21, LN10: "2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286" }, q = true, oe = "[DecimalError] ", Ie = oe + "Invalid argument: ", Cr = oe + "Exponent out of range: ", Ne = Math.floor, Oe = Math.pow, ds = /^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i, J = 1e7, N = 7, hn = 9007199254740991, Dt = Ne(hn / N), S = {};
      S.absoluteValue = S.abs = function() {
        var t = new this.constructor(this);
        return t.s && (t.s = 1), t;
      };
      S.comparedTo = S.cmp = function(t) {
        var e, r, n, i, o = this;
        if (t = new o.constructor(t), o.s !== t.s) return o.s || -t.s;
        if (o.e !== t.e) return o.e > t.e ^ o.s < 0 ? 1 : -1;
        for (n = o.d.length, i = t.d.length, e = 0, r = n < i ? n : i; e < r; ++e) if (o.d[e] !== t.d[e]) return o.d[e] > t.d[e] ^ o.s < 0 ? 1 : -1;
        return n === i ? 0 : n > i ^ o.s < 0 ? 1 : -1;
      };
      S.decimalPlaces = S.dp = function() {
        var t = this, e = t.d.length - 1, r = (e - t.e) * N;
        if (e = t.d[e], e) for (; e % 10 == 0; e /= 10) r--;
        return r < 0 ? 0 : r;
      };
      S.dividedBy = S.div = function(t) {
        return be(this, new this.constructor(t));
      };
      S.dividedToIntegerBy = S.idiv = function(t) {
        var e = this, r = e.constructor;
        return L(be(e, new r(t), 0, 1), r.precision);
      };
      S.equals = S.eq = function(t) {
        return !this.cmp(t);
      };
      S.exponent = function() {
        return V(this);
      };
      S.greaterThan = S.gt = function(t) {
        return this.cmp(t) > 0;
      };
      S.greaterThanOrEqualTo = S.gte = function(t) {
        return this.cmp(t) >= 0;
      };
      S.isInteger = S.isint = function() {
        return this.e > this.d.length - 2;
      };
      S.isNegative = S.isneg = function() {
        return this.s < 0;
      };
      S.isPositive = S.ispos = function() {
        return this.s > 0;
      };
      S.isZero = function() {
        return this.s === 0;
      };
      S.lessThan = S.lt = function(t) {
        return this.cmp(t) < 0;
      };
      S.lessThanOrEqualTo = S.lte = function(t) {
        return this.cmp(t) < 1;
      };
      S.logarithm = S.log = function(t) {
        var e, r = this, n = r.constructor, i = n.precision, o = i + 5;
        if (t === void 0) t = new n(10);
        else if (t = new n(t), t.s < 1 || t.eq(te)) throw Error(oe + "NaN");
        if (r.s < 1) throw Error(oe + (r.s ? "NaN" : "-Infinity"));
        return r.eq(te) ? new n(0) : (q = false, e = be(st(r, o), st(t, o), o), q = true, L(e, i));
      };
      S.minus = S.sub = function(t) {
        var e = this;
        return t = new e.constructor(t), e.s == t.s ? En(e, t) : bn(e, (t.s = -t.s, t));
      };
      S.modulo = S.mod = function(t) {
        var e, r = this, n = r.constructor, i = n.precision;
        if (t = new n(t), !t.s) throw Error(oe + "NaN");
        return r.s ? (q = false, e = be(r, t, 0, 1).times(t), q = true, r.minus(e)) : L(new n(r), i);
      };
      S.naturalExponential = S.exp = function() {
        return wn(this);
      };
      S.naturalLogarithm = S.ln = function() {
        return st(this);
      };
      S.negated = S.neg = function() {
        var t = new this.constructor(this);
        return t.s = -t.s || 0, t;
      };
      S.plus = S.add = function(t) {
        var e = this;
        return t = new e.constructor(t), e.s == t.s ? bn(e, t) : En(e, (t.s = -t.s, t));
      };
      S.precision = S.sd = function(t) {
        var e, r, n, i = this;
        if (t !== void 0 && t !== !!t && t !== 1 && t !== 0) throw Error(Ie + t);
        if (e = V(i) + 1, n = i.d.length - 1, r = n * N + 1, n = i.d[n], n) {
          for (; n % 10 == 0; n /= 10) r--;
          for (n = i.d[0]; n >= 10; n /= 10) r++;
        }
        return t && e > r ? e : r;
      };
      S.squareRoot = S.sqrt = function() {
        var t, e, r, n, i, o, s, a = this, f = a.constructor;
        if (a.s < 1) {
          if (!a.s) return new f(0);
          throw Error(oe + "NaN");
        }
        for (t = V(a), q = false, i = Math.sqrt(+a), i == 0 || i == 1 / 0 ? (e = ce(a.d), (e.length + t) % 2 == 0 && (e += "0"), i = Math.sqrt(e), t = Ne((t + 1) / 2) - (t < 0 || t % 2), i == 1 / 0 ? e = "5e" + t : (e = i.toExponential(), e = e.slice(0, e.indexOf("e") + 1) + t), n = new f(e)) : n = new f(i.toString()), r = f.precision, i = s = r + 3; ; ) if (o = n, n = o.plus(be(a, o, s + 2)).times(0.5), ce(o.d).slice(0, s) === (e = ce(n.d)).slice(0, s)) {
          if (e = e.slice(s - 3, s + 1), i == s && e == "4999") {
            if (L(o, r + 1, 0), o.times(o).eq(a)) {
              n = o;
              break;
            }
          } else if (e != "9999") break;
          s += 4;
        }
        return q = true, L(n, r);
      };
      S.times = S.mul = function(t) {
        var e, r, n, i, o, s, a, f, y, C = this, R = C.constructor, O = C.d, A = (t = new R(t)).d;
        if (!C.s || !t.s) return new R(0);
        for (t.s *= C.s, r = C.e + t.e, f = O.length, y = A.length, f < y && (o = O, O = A, A = o, s = f, f = y, y = s), o = [], s = f + y, n = s; n--; ) o.push(0);
        for (n = y; --n >= 0; ) {
          for (e = 0, i = f + n; i > n; ) a = o[i] + A[n] * O[i - n - 1] + e, o[i--] = a % J | 0, e = a / J | 0;
          o[i] = (o[i] + e) % J | 0;
        }
        for (; !o[--s]; ) o.pop();
        return e ? ++r : o.shift(), t.d = o, t.e = r, q ? L(t, R.precision) : t;
      };
      S.toDecimalPlaces = S.todp = function(t, e) {
        var r = this, n = r.constructor;
        return r = new n(r), t === void 0 ? r : (me(t, 0, Ue), e === void 0 ? e = n.rounding : me(e, 0, 8), L(r, t + V(r) + 1, e));
      };
      S.toExponential = function(t, e) {
        var r, n = this, i = n.constructor;
        return t === void 0 ? r = Me(n, true) : (me(t, 0, Ue), e === void 0 ? e = i.rounding : me(e, 0, 8), n = L(new i(n), t + 1, e), r = Me(n, true, t + 1)), r;
      };
      S.toFixed = function(t, e) {
        var r, n, i = this, o = i.constructor;
        return t === void 0 ? Me(i) : (me(t, 0, Ue), e === void 0 ? e = o.rounding : me(e, 0, 8), n = L(new o(i), t + V(i) + 1, e), r = Me(n.abs(), false, t + V(n) + 1), i.isneg() && !i.isZero() ? "-" + r : r);
      };
      S.toInteger = S.toint = function() {
        var t = this, e = t.constructor;
        return L(new e(t), V(t) + 1, e.rounding);
      };
      S.toNumber = function() {
        return +this;
      };
      S.toPower = S.pow = function(t) {
        var e, r, n, i, o, s, a = this, f = a.constructor, y = 12, C = +(t = new f(t));
        if (!t.s) return new f(te);
        if (a = new f(a), !a.s) {
          if (t.s < 1) throw Error(oe + "Infinity");
          return a;
        }
        if (a.eq(te)) return a;
        if (n = f.precision, t.eq(te)) return L(a, n);
        if (e = t.e, r = t.d.length - 1, s = e >= r, o = a.s, s) {
          if ((r = C < 0 ? -C : C) <= hn) {
            for (i = new f(te), e = Math.ceil(n / N + 4), q = false; r % 2 && (i = i.times(a), yn(i.d, e)), r = Ne(r / 2), r !== 0; ) a = a.times(a), yn(a.d, e);
            return q = true, t.s < 0 ? new f(te).div(i) : L(i, n);
          }
        } else if (o < 0) throw Error(oe + "NaN");
        return o = o < 0 && t.d[Math.max(e, r)] & 1 ? -1 : 1, a.s = 1, q = false, i = t.times(st(a, n + y)), q = true, i = wn(i), i.s = o, i;
      };
      S.toPrecision = function(t, e) {
        var r, n, i = this, o = i.constructor;
        return t === void 0 ? (r = V(i), n = Me(i, r <= o.toExpNeg || r >= o.toExpPos)) : (me(t, 1, Ue), e === void 0 ? e = o.rounding : me(e, 0, 8), i = L(new o(i), t, e), r = V(i), n = Me(i, t <= r || r <= o.toExpNeg, t)), n;
      };
      S.toSignificantDigits = S.tosd = function(t, e) {
        var r = this, n = r.constructor;
        return t === void 0 ? (t = n.precision, e = n.rounding) : (me(t, 1, Ue), e === void 0 ? e = n.rounding : me(e, 0, 8)), L(new n(r), t, e);
      };
      S.toString = S.valueOf = S.val = S.toJSON = S[Symbol.for("nodejs.util.inspect.custom")] = function() {
        var t = this, e = V(t), r = t.constructor;
        return Me(t, e <= r.toExpNeg || e >= r.toExpPos);
      };
      be = /* @__PURE__ */ function() {
        function t(n, i) {
          var o, s = 0, a = n.length;
          for (n = n.slice(); a--; ) o = n[a] * i + s, n[a] = o % J | 0, s = o / J | 0;
          return s && n.unshift(s), n;
        }
        __name(t, "t");
        function e(n, i, o, s) {
          var a, f;
          if (o != s) f = o > s ? 1 : -1;
          else for (a = f = 0; a < o; a++) if (n[a] != i[a]) {
            f = n[a] > i[a] ? 1 : -1;
            break;
          }
          return f;
        }
        __name(e, "e");
        function r(n, i, o) {
          for (var s = 0; o--; ) n[o] -= s, s = n[o] < i[o] ? 1 : 0, n[o] = s * J + n[o] - i[o];
          for (; !n[0] && n.length > 1; ) n.shift();
        }
        __name(r, "r");
        return function(n, i, o, s) {
          var a, f, y, C, R, O, A, I, k, M, se, z, F, Y, ke, xr, ae, kt, Ot = n.constructor, Yo = n.s == i.s ? 1 : -1, ue = n.d, B = i.d;
          if (!n.s) return new Ot(n);
          if (!i.s) throw Error(oe + "Division by zero");
          for (f = n.e - i.e, ae = B.length, ke = ue.length, A = new Ot(Yo), I = A.d = [], y = 0; B[y] == (ue[y] || 0); ) ++y;
          if (B[y] > (ue[y] || 0) && --f, o == null ? z = o = Ot.precision : s ? z = o + (V(n) - V(i)) + 1 : z = o, z < 0) return new Ot(0);
          if (z = z / N + 2 | 0, y = 0, ae == 1) for (C = 0, B = B[0], z++; (y < ke || C) && z--; y++) F = C * J + (ue[y] || 0), I[y] = F / B | 0, C = F % B | 0;
          else {
            for (C = J / (B[0] + 1) | 0, C > 1 && (B = t(B, C), ue = t(ue, C), ae = B.length, ke = ue.length), Y = ae, k = ue.slice(0, ae), M = k.length; M < ae; ) k[M++] = 0;
            kt = B.slice(), kt.unshift(0), xr = B[0], B[1] >= J / 2 && ++xr;
            do
              C = 0, a = e(B, k, ae, M), a < 0 ? (se = k[0], ae != M && (se = se * J + (k[1] || 0)), C = se / xr | 0, C > 1 ? (C >= J && (C = J - 1), R = t(B, C), O = R.length, M = k.length, a = e(R, k, O, M), a == 1 && (C--, r(R, ae < O ? kt : B, O))) : (C == 0 && (a = C = 1), R = B.slice()), O = R.length, O < M && R.unshift(0), r(k, R, M), a == -1 && (M = k.length, a = e(B, k, ae, M), a < 1 && (C++, r(k, ae < M ? kt : B, M))), M = k.length) : a === 0 && (C++, k = [0]), I[y++] = C, a && k[0] ? k[M++] = ue[Y] || 0 : (k = [ue[Y]], M = 1);
            while ((Y++ < ke || k[0] !== void 0) && z--);
          }
          return I[0] || I.shift(), A.e = f, L(A, s ? o + V(A) + 1 : o);
        };
      }();
      Rr = xn(ps);
      te = new Rr(1);
      _t = Rr;
    });
    var T;
    var pe;
    var l = ie(() => {
      "use strict";
      Pn();
      T = class extends _t {
        static {
          __name(this, "T");
        }
        static isDecimal(e) {
          return e instanceof _t;
        }
        static random(e = 20) {
          {
            let n = globalThis.crypto.getRandomValues(new Uint8Array(e)).reduce((i, o) => i + o, "");
            return new _t(`0.${n.slice(0, e)}`);
          }
        }
      }, pe = T;
    });
    function Es() {
      return false;
    }
    __name(Es, "Es");
    function Ir() {
      return { dev: 0, ino: 0, mode: 0, nlink: 0, uid: 0, gid: 0, rdev: 0, size: 0, blksize: 0, blocks: 0, atimeMs: 0, mtimeMs: 0, ctimeMs: 0, birthtimeMs: 0, atime: /* @__PURE__ */ new Date(), mtime: /* @__PURE__ */ new Date(), ctime: /* @__PURE__ */ new Date(), birthtime: /* @__PURE__ */ new Date() };
    }
    __name(Ir, "Ir");
    function xs() {
      return Ir();
    }
    __name(xs, "xs");
    function Ps() {
      return [];
    }
    __name(Ps, "Ps");
    function vs(t) {
      t(null, []);
    }
    __name(vs, "vs");
    function Ts() {
      return "";
    }
    __name(Ts, "Ts");
    function Cs() {
      return "";
    }
    __name(Cs, "Cs");
    function Rs() {
    }
    __name(Rs, "Rs");
    function As() {
    }
    __name(As, "As");
    function Ss() {
    }
    __name(Ss, "Ss");
    function ks() {
    }
    __name(ks, "ks");
    function Os() {
    }
    __name(Os, "Os");
    function Is() {
    }
    __name(Is, "Is");
    function Ms() {
    }
    __name(Ms, "Ms");
    function Ds() {
    }
    __name(Ds, "Ds");
    function _s() {
      return { close: /* @__PURE__ */ __name(() => {
      }, "close"), on: /* @__PURE__ */ __name(() => {
      }, "on"), removeAllListeners: /* @__PURE__ */ __name(() => {
      }, "removeAllListeners") };
    }
    __name(_s, "_s");
    function Ls(t, e) {
      e(null, Ir());
    }
    __name(Ls, "Ls");
    var Fs;
    var Us;
    var qn;
    var Bn = ie(() => {
      "use strict";
      u();
      c();
      m();
      p();
      d();
      l();
      Fs = {}, Us = { existsSync: Es, lstatSync: Ir, stat: Ls, statSync: xs, readdirSync: Ps, readdir: vs, readlinkSync: Ts, realpathSync: Cs, chmodSync: Rs, renameSync: As, mkdirSync: Ss, rmdirSync: ks, rmSync: Os, unlinkSync: Is, watchFile: Ms, unwatchFile: Ds, watch: _s, promises: Fs }, qn = Us;
    });
    function Ns(...t) {
      return t.join("/");
    }
    __name(Ns, "Ns");
    function qs(...t) {
      return t.join("/");
    }
    __name(qs, "qs");
    function Bs(t) {
      let e = $n(t), r = Vn(t), [n, i] = e.split(".");
      return { root: "/", dir: r, base: e, ext: i, name: n };
    }
    __name(Bs, "Bs");
    function $n(t) {
      let e = t.split("/");
      return e[e.length - 1];
    }
    __name($n, "$n");
    function Vn(t) {
      return t.split("/").slice(0, -1).join("/");
    }
    __name(Vn, "Vn");
    function Vs(t) {
      let e = t.split("/").filter((i) => i !== "" && i !== "."), r = [];
      for (let i of e) i === ".." ? r.pop() : r.push(i);
      let n = r.join("/");
      return t.startsWith("/") ? "/" + n : n;
    }
    __name(Vs, "Vs");
    var jn;
    var $s;
    var js;
    var Qs;
    var Nt;
    var Qn = ie(() => {
      "use strict";
      u();
      c();
      m();
      p();
      d();
      l();
      jn = "/", $s = ":";
      js = { sep: jn }, Qs = { basename: $n, delimiter: $s, dirname: Vn, join: qs, normalize: Vs, parse: Bs, posix: js, resolve: Ns, sep: jn }, Nt = Qs;
    });
    var Jn = Fe((am, Js) => {
      Js.exports = { name: "@prisma/internals", version: "6.13.0", description: "This package is intended for Prisma's internal use", main: "dist/index.js", types: "dist/index.d.ts", repository: { type: "git", url: "https://github.com/prisma/prisma.git", directory: "packages/internals" }, homepage: "https://www.prisma.io", author: "Tim Suchanek <<EMAIL>>", bugs: "https://github.com/prisma/prisma/issues", license: "Apache-2.0", scripts: { dev: "DEV=true tsx helpers/build.ts", build: "tsx helpers/build.ts", test: "dotenv -e ../../.db.env -- jest --silent", prepublishOnly: "pnpm run build" }, files: ["README.md", "dist", "!**/libquery_engine*", "!dist/get-generators/engines/*", "scripts"], devDependencies: { "@babel/helper-validator-identifier": "7.25.9", "@opentelemetry/api": "1.9.0", "@swc/core": "1.11.5", "@swc/jest": "0.2.37", "@types/babel__helper-validator-identifier": "7.15.2", "@types/jest": "29.5.14", "@types/node": "18.19.76", "@types/resolve": "1.20.6", archiver: "6.0.2", "checkpoint-client": "1.1.33", "cli-truncate": "4.0.0", dotenv: "16.5.0", esbuild: "0.25.5", "escape-string-regexp": "5.0.0", execa: "5.1.1", "fast-glob": "3.3.3", "find-up": "7.0.0", "fp-ts": "2.16.9", "fs-extra": "11.3.0", "fs-jetpack": "5.1.0", "global-dirs": "4.0.0", globby: "11.1.0", "identifier-regex": "1.0.0", "indent-string": "4.0.0", "is-windows": "1.0.2", "is-wsl": "3.1.0", jest: "29.7.0", "jest-junit": "16.0.0", kleur: "4.1.5", "mock-stdin": "1.0.0", "new-github-issue-url": "0.2.1", "node-fetch": "3.3.2", "npm-packlist": "5.1.3", open: "7.4.2", "p-map": "4.0.0", "read-package-up": "11.0.0", resolve: "1.22.10", "string-width": "7.2.0", "strip-ansi": "6.0.1", "strip-indent": "4.0.0", "temp-dir": "2.0.0", tempy: "1.0.1", "terminal-link": "4.0.0", tmp: "0.2.3", "ts-node": "10.9.2", "ts-pattern": "5.6.2", "ts-toolbelt": "9.6.0", typescript: "5.4.5", yarn: "1.22.22" }, dependencies: { "@prisma/config": "workspace:*", "@prisma/debug": "workspace:*", "@prisma/dmmf": "workspace:*", "@prisma/driver-adapter-utils": "workspace:*", "@prisma/engines": "workspace:*", "@prisma/fetch-engine": "workspace:*", "@prisma/generator": "workspace:*", "@prisma/generator-helper": "workspace:*", "@prisma/get-platform": "workspace:*", "@prisma/prisma-schema-wasm": "6.13.0-35.361e86d0ea4987e9f53a565309b3eed797a6bcbd", "@prisma/schema-engine-wasm": "6.13.0-35.361e86d0ea4987e9f53a565309b3eed797a6bcbd", "@prisma/schema-files-loader": "workspace:*", arg: "5.0.2", prompts: "2.4.2" }, peerDependencies: { typescript: ">=5.1.0" }, peerDependenciesMeta: { typescript: { optional: true } }, sideEffects: false };
    });
    var zn = Fe((wp, Hn) => {
      "use strict";
      u();
      c();
      m();
      p();
      d();
      l();
      Hn.exports = (t, e = 1, r) => {
        if (r = { indent: " ", includeEmptyLines: false, ...r }, typeof t != "string") throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof t}\``);
        if (typeof e != "number") throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof e}\``);
        if (typeof r.indent != "string") throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);
        if (e === 0) return t;
        let n = r.includeEmptyLines ? /^/gm : /^(?!\s*$)/gm;
        return t.replace(n, r.indent.repeat(e));
      };
    });
    var Zn = Fe((Dp, Xn) => {
      "use strict";
      u();
      c();
      m();
      p();
      d();
      l();
      Xn.exports = ({ onlyFirst: t = false } = {}) => {
        let e = ["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)", "(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");
        return new RegExp(e, t ? void 0 : "g");
      };
    });
    var ti = Fe((Bp, ei) => {
      "use strict";
      u();
      c();
      m();
      p();
      d();
      l();
      var ta = Zn();
      ei.exports = (t) => typeof t == "string" ? t.replace(ta(), "") : t;
    });
    var $r = Fe((ih, si) => {
      "use strict";
      u();
      c();
      m();
      p();
      d();
      l();
      si.exports = /* @__PURE__ */ function() {
        function t(e, r, n, i, o) {
          return e < r || n < r ? e > n ? n + 1 : e + 1 : i === o ? r : r + 1;
        }
        __name(t, "t");
        return function(e, r) {
          if (e === r) return 0;
          if (e.length > r.length) {
            var n = e;
            e = r, r = n;
          }
          for (var i = e.length, o = r.length; i > 0 && e.charCodeAt(i - 1) === r.charCodeAt(o - 1); ) i--, o--;
          for (var s = 0; s < i && e.charCodeAt(s) === r.charCodeAt(s); ) s++;
          if (i -= s, o -= s, i === 0 || o < 3) return o;
          var a = 0, f, y, C, R, O, A, I, k, M, se, z, F, Y = [];
          for (f = 0; f < i; f++) Y.push(f + 1), Y.push(e.charCodeAt(s + f));
          for (var ke = Y.length - 1; a < o - 3; ) for (M = r.charCodeAt(s + (y = a)), se = r.charCodeAt(s + (C = a + 1)), z = r.charCodeAt(s + (R = a + 2)), F = r.charCodeAt(s + (O = a + 3)), A = a += 4, f = 0; f < ke; f += 2) I = Y[f], k = Y[f + 1], y = t(I, y, C, M, k), C = t(y, C, R, se, k), R = t(C, R, O, z, k), A = t(R, O, A, F, k), Y[f] = A, O = R, R = C, C = y, y = I;
          for (; a < o; ) for (M = r.charCodeAt(s + (y = a)), A = ++a, f = 0; f < ke; f += 2) I = Y[f], Y[f] = A = t(I, y, A, M, Y[f + 1]), y = I;
          return A;
        };
      }();
    });
    var mi = ie(() => {
      "use strict";
      u();
      c();
      m();
      p();
      d();
      l();
    });
    var pi = ie(() => {
      "use strict";
      u();
      c();
      m();
      p();
      d();
      l();
    });
    var Fi = Fe((ov, Ka) => {
      Ka.exports = { name: "@prisma/engines-version", version: "6.13.0-35.361e86d0ea4987e9f53a565309b3eed797a6bcbd", main: "index.js", types: "index.d.ts", license: "Apache-2.0", author: "Tim Suchanek <<EMAIL>>", prisma: { enginesVersion: "361e86d0ea4987e9f53a565309b3eed797a6bcbd" }, repository: { type: "git", url: "https://github.com/prisma/engines-wrapper.git", directory: "packages/engines-version" }, devDependencies: { "@types/node": "18.19.76", typescript: "4.9.5" }, files: ["index.js", "index.d.ts"], scripts: { build: "tsc -d" } };
    });
    var sr;
    var Ui = ie(() => {
      "use strict";
      u();
      c();
      m();
      p();
      d();
      l();
      sr = class {
        static {
          __name(this, "sr");
        }
        events = {};
        on(e, r) {
          return this.events[e] || (this.events[e] = []), this.events[e].push(r), this;
        }
        emit(e, ...r) {
          return this.events[e] ? (this.events[e].forEach((n) => {
            n(...r);
          }), true) : false;
        }
      };
    });
    var Zl = {};
    it(Zl, { DMMF: /* @__PURE__ */ __name(() => dt, "DMMF"), Debug: /* @__PURE__ */ __name(() => G, "Debug"), Decimal: /* @__PURE__ */ __name(() => pe, "Decimal"), Extensions: /* @__PURE__ */ __name(() => Ar, "Extensions"), MetricsClient: /* @__PURE__ */ __name(() => Ze, "MetricsClient"), PrismaClientInitializationError: /* @__PURE__ */ __name(() => D, "PrismaClientInitializationError"), PrismaClientKnownRequestError: /* @__PURE__ */ __name(() => Z, "PrismaClientKnownRequestError"), PrismaClientRustPanicError: /* @__PURE__ */ __name(() => xe, "PrismaClientRustPanicError"), PrismaClientUnknownRequestError: /* @__PURE__ */ __name(() => Q, "PrismaClientUnknownRequestError"), PrismaClientValidationError: /* @__PURE__ */ __name(() => K, "PrismaClientValidationError"), Public: /* @__PURE__ */ __name(() => Sr, "Public"), Sql: /* @__PURE__ */ __name(() => ee, "Sql"), createParam: /* @__PURE__ */ __name(() => Si, "createParam"), defineDmmfProperty: /* @__PURE__ */ __name(() => _i, "defineDmmfProperty"), deserializeJsonResponse: /* @__PURE__ */ __name(() => Ve, "deserializeJsonResponse"), deserializeRawResult: /* @__PURE__ */ __name(() => wr, "deserializeRawResult"), dmmfToRuntimeDataModel: /* @__PURE__ */ __name(() => oi, "dmmfToRuntimeDataModel"), empty: /* @__PURE__ */ __name(() => qi, "empty"), getPrismaClient: /* @__PURE__ */ __name(() => Ko, "getPrismaClient"), getRuntime: /* @__PURE__ */ __name(() => Ae, "getRuntime"), join: /* @__PURE__ */ __name(() => Ni, "join"), makeStrictEnum: /* @__PURE__ */ __name(() => Ho, "makeStrictEnum"), makeTypedQueryFactory: /* @__PURE__ */ __name(() => Li, "makeTypedQueryFactory"), objectEnumValues: /* @__PURE__ */ __name(() => zt, "objectEnumValues"), raw: /* @__PURE__ */ __name(() => zr, "raw"), serializeJsonQuery: /* @__PURE__ */ __name(() => nr, "serializeJsonQuery"), skip: /* @__PURE__ */ __name(() => rr, "skip"), sqltag: /* @__PURE__ */ __name(() => Yr, "sqltag"), warnEnvConflicts: /* @__PURE__ */ __name(() => void 0, "warnEnvConflicts"), warnOnce: /* @__PURE__ */ __name(() => ct, "warnOnce") });
    module.exports = ns(Zl);
    u();
    c();
    m();
    p();
    d();
    l();
    var Ar = {};
    it(Ar, { defineExtension: /* @__PURE__ */ __name(() => vn, "defineExtension"), getExtensionContext: /* @__PURE__ */ __name(() => Tn, "getExtensionContext") });
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    function vn(t) {
      return typeof t == "function" ? t : (e) => e.$extends(t);
    }
    __name(vn, "vn");
    u();
    c();
    m();
    p();
    d();
    l();
    function Tn(t) {
      return t;
    }
    __name(Tn, "Tn");
    var Sr = {};
    it(Sr, { validator: /* @__PURE__ */ __name(() => Cn, "validator") });
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    function Cn(...t) {
      return (e) => e;
    }
    __name(Cn, "Cn");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    var kr;
    var Rn;
    var An;
    var Sn;
    var kn = true;
    typeof g < "u" && ({ FORCE_COLOR: kr, NODE_DISABLE_COLORS: Rn, NO_COLOR: An, TERM: Sn } = g.env || {}, kn = g.stdout && g.stdout.isTTY);
    var gs = { enabled: !Rn && An == null && Sn !== "dumb" && (kr != null && kr !== "0" || kn) };
    function U(t, e) {
      let r = new RegExp(`\\x1b\\[${e}m`, "g"), n = `\x1B[${t}m`, i = `\x1B[${e}m`;
      return function(o) {
        return !gs.enabled || o == null ? o : n + (~("" + o).indexOf(i) ? o.replace(r, i + n) : o) + i;
      };
    }
    __name(U, "U");
    var Yu = U(0, 0);
    var Lt = U(1, 22);
    var Ft = U(2, 22);
    var Xu = U(3, 23);
    var On = U(4, 24);
    var Zu = U(7, 27);
    var ec = U(8, 28);
    var tc = U(9, 29);
    var rc = U(30, 39);
    var qe = U(31, 39);
    var In = U(32, 39);
    var Mn = U(33, 39);
    var Dn = U(34, 39);
    var nc = U(35, 39);
    var _n = U(36, 39);
    var ic = U(37, 39);
    var Ln = U(90, 39);
    var oc = U(90, 39);
    var sc = U(40, 49);
    var ac = U(41, 49);
    var lc = U(42, 49);
    var uc = U(43, 49);
    var cc = U(44, 49);
    var mc = U(45, 49);
    var pc = U(46, 49);
    var dc = U(47, 49);
    u();
    c();
    m();
    p();
    d();
    l();
    var ys = 100;
    var Fn = ["green", "yellow", "blue", "magenta", "cyan", "red"];
    var Ut = [];
    var Un = Date.now();
    var hs = 0;
    var Or = typeof g < "u" ? g.env : {};
    globalThis.DEBUG ??= Or.DEBUG ?? "";
    globalThis.DEBUG_COLORS ??= Or.DEBUG_COLORS ? Or.DEBUG_COLORS === "true" : true;
    var at = { enable(t) {
      typeof t == "string" && (globalThis.DEBUG = t);
    }, disable() {
      let t = globalThis.DEBUG;
      return globalThis.DEBUG = "", t;
    }, enabled(t) {
      let e = globalThis.DEBUG.split(",").map((i) => i.replace(/[.+?^${}()|[\]\\]/g, "\\$&")), r = e.some((i) => i === "" || i[0] === "-" ? false : t.match(RegExp(i.split("*").join(".*") + "$"))), n = e.some((i) => i === "" || i[0] !== "-" ? false : t.match(RegExp(i.slice(1).split("*").join(".*") + "$")));
      return r && !n;
    }, log: /* @__PURE__ */ __name((...t) => {
      let [e, r, ...n] = t;
      (console.warn ?? console.log)(`${e} ${r}`, ...n);
    }, "log"), formatters: {} };
    function bs(t) {
      let e = { color: Fn[hs++ % Fn.length], enabled: at.enabled(t), namespace: t, log: at.log, extend: /* @__PURE__ */ __name(() => {
      }, "extend") }, r = /* @__PURE__ */ __name((...n) => {
        let { enabled: i, namespace: o, color: s, log: a } = e;
        if (n.length !== 0 && Ut.push([o, ...n]), Ut.length > ys && Ut.shift(), at.enabled(o) || i) {
          let f = n.map((C) => typeof C == "string" ? C : ws(C)), y = `+${Date.now() - Un}ms`;
          Un = Date.now(), a(o, ...f, y);
        }
      }, "r");
      return new Proxy(r, { get: /* @__PURE__ */ __name((n, i) => e[i], "get"), set: /* @__PURE__ */ __name((n, i, o) => e[i] = o, "set") });
    }
    __name(bs, "bs");
    var G = new Proxy(bs, { get: /* @__PURE__ */ __name((t, e) => at[e], "get"), set: /* @__PURE__ */ __name((t, e, r) => at[e] = r, "set") });
    function ws(t, e = 2) {
      let r = /* @__PURE__ */ new Set();
      return JSON.stringify(t, (n, i) => {
        if (typeof i == "object" && i !== null) {
          if (r.has(i)) return "[Circular *]";
          r.add(i);
        } else if (typeof i == "bigint") return i.toString();
        return i;
      }, e);
    }
    __name(ws, "ws");
    function Nn() {
      Ut.length = 0;
    }
    __name(Nn, "Nn");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    var Mr = ["darwin", "darwin-arm64", "debian-openssl-1.0.x", "debian-openssl-1.1.x", "debian-openssl-3.0.x", "rhel-openssl-1.0.x", "rhel-openssl-1.1.x", "rhel-openssl-3.0.x", "linux-arm64-openssl-1.1.x", "linux-arm64-openssl-1.0.x", "linux-arm64-openssl-3.0.x", "linux-arm-openssl-1.1.x", "linux-arm-openssl-1.0.x", "linux-arm-openssl-3.0.x", "linux-musl", "linux-musl-openssl-3.0.x", "linux-musl-arm64-openssl-1.1.x", "linux-musl-arm64-openssl-3.0.x", "linux-nixos", "linux-static-x64", "linux-static-arm64", "windows", "freebsd11", "freebsd12", "freebsd13", "freebsd14", "freebsd15", "openbsd", "netbsd", "arm"];
    u();
    c();
    m();
    p();
    d();
    l();
    var Gs = Jn();
    var Dr = Gs.version;
    u();
    c();
    m();
    p();
    d();
    l();
    function Be(t) {
      let e = Ws();
      return e || (t?.config.engineType === "library" ? "library" : t?.config.engineType === "binary" ? "binary" : t?.config.engineType === "client" ? "client" : Ks(t));
    }
    __name(Be, "Be");
    function Ws() {
      let t = g.env.PRISMA_CLIENT_ENGINE_TYPE;
      return t === "library" ? "library" : t === "binary" ? "binary" : t === "client" ? "client" : void 0;
    }
    __name(Ws, "Ws");
    function Ks(t) {
      return t?.previewFeatures.includes("queryCompiler") ? "client" : "library";
    }
    __name(Ks, "Ks");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    function _r(t) {
      return t.name === "DriverAdapterError" && typeof t.cause == "object";
    }
    __name(_r, "_r");
    u();
    c();
    m();
    p();
    d();
    l();
    function qt(t) {
      return { ok: true, value: t, map(e) {
        return qt(e(t));
      }, flatMap(e) {
        return e(t);
      } };
    }
    __name(qt, "qt");
    function De(t) {
      return { ok: false, error: t, map() {
        return De(t);
      }, flatMap() {
        return De(t);
      } };
    }
    __name(De, "De");
    var Gn = G("driver-adapter-utils");
    var Lr = class {
      static {
        __name(this, "Lr");
      }
      registeredErrors = [];
      consumeError(e) {
        return this.registeredErrors[e];
      }
      registerNewError(e) {
        let r = 0;
        for (; this.registeredErrors[r] !== void 0; ) r++;
        return this.registeredErrors[r] = { error: e }, r;
      }
    };
    var Bt = /* @__PURE__ */ __name((t, e = new Lr()) => {
      let r = { adapterName: t.adapterName, errorRegistry: e, queryRaw: we(e, t.queryRaw.bind(t)), executeRaw: we(e, t.executeRaw.bind(t)), executeScript: we(e, t.executeScript.bind(t)), dispose: we(e, t.dispose.bind(t)), provider: t.provider, startTransaction: /* @__PURE__ */ __name(async (...n) => (await we(e, t.startTransaction.bind(t))(...n)).map((o) => Hs(e, o)), "startTransaction") };
      return t.getConnectionInfo && (r.getConnectionInfo = zs(e, t.getConnectionInfo.bind(t))), r;
    }, "Bt");
    var Hs = /* @__PURE__ */ __name((t, e) => ({ adapterName: e.adapterName, provider: e.provider, options: e.options, queryRaw: we(t, e.queryRaw.bind(e)), executeRaw: we(t, e.executeRaw.bind(e)), commit: we(t, e.commit.bind(e)), rollback: we(t, e.rollback.bind(e)) }), "Hs");
    function we(t, e) {
      return async (...r) => {
        try {
          return qt(await e(...r));
        } catch (n) {
          if (Gn("[error@wrapAsync]", n), _r(n)) return De(n.cause);
          let i = t.registerNewError(n);
          return De({ kind: "GenericJs", id: i });
        }
      };
    }
    __name(we, "we");
    function zs(t, e) {
      return (...r) => {
        try {
          return qt(e(...r));
        } catch (n) {
          if (Gn("[error@wrapSync]", n), _r(n)) return De(n.cause);
          let i = t.registerNewError(n);
          return De({ kind: "GenericJs", id: i });
        }
      };
    }
    __name(zs, "zs");
    u();
    c();
    m();
    p();
    d();
    l();
    var Wn = "prisma+postgres";
    var Kn = `${Wn}:`;
    function Fr(t) {
      return t?.toString().startsWith(`${Kn}//`) ?? false;
    }
    __name(Fr, "Fr");
    var ut = {};
    it(ut, { error: /* @__PURE__ */ __name(() => Zs, "error"), info: /* @__PURE__ */ __name(() => Xs, "info"), log: /* @__PURE__ */ __name(() => Ys, "log"), query: /* @__PURE__ */ __name(() => ea, "query"), should: /* @__PURE__ */ __name(() => Yn, "should"), tags: /* @__PURE__ */ __name(() => lt, "tags"), warn: /* @__PURE__ */ __name(() => Ur, "warn") });
    u();
    c();
    m();
    p();
    d();
    l();
    var lt = { error: qe("prisma:error"), warn: Mn("prisma:warn"), info: _n("prisma:info"), query: Dn("prisma:query") };
    var Yn = { warn: /* @__PURE__ */ __name(() => !g.env.PRISMA_DISABLE_WARNINGS, "warn") };
    function Ys(...t) {
      console.log(...t);
    }
    __name(Ys, "Ys");
    function Ur(t, ...e) {
      Yn.warn() && console.warn(`${lt.warn} ${t}`, ...e);
    }
    __name(Ur, "Ur");
    function Xs(t, ...e) {
      console.info(`${lt.info} ${t}`, ...e);
    }
    __name(Xs, "Xs");
    function Zs(t, ...e) {
      console.error(`${lt.error} ${t}`, ...e);
    }
    __name(Zs, "Zs");
    function ea(t, ...e) {
      console.log(`${lt.query} ${t}`, ...e);
    }
    __name(ea, "ea");
    u();
    c();
    m();
    p();
    d();
    l();
    function $t(t, e) {
      if (!t) throw new Error(`${e}. This should never happen. If you see this error, please, open an issue at https://pris.ly/prisma-prisma-bug-report`);
    }
    __name($t, "$t");
    u();
    c();
    m();
    p();
    d();
    l();
    function Ee(t, e) {
      throw new Error(e);
    }
    __name(Ee, "Ee");
    u();
    c();
    m();
    p();
    d();
    l();
    function Nr(t, e) {
      return Object.prototype.hasOwnProperty.call(t, e);
    }
    __name(Nr, "Nr");
    u();
    c();
    m();
    p();
    d();
    l();
    function $e(t, e) {
      let r = {};
      for (let n of Object.keys(t)) r[n] = e(t[n], n);
      return r;
    }
    __name($e, "$e");
    u();
    c();
    m();
    p();
    d();
    l();
    function qr(t, e) {
      if (t.length === 0) return;
      let r = t[0];
      for (let n = 1; n < t.length; n++) e(r, t[n]) < 0 && (r = t[n]);
      return r;
    }
    __name(qr, "qr");
    u();
    c();
    m();
    p();
    d();
    l();
    function re(t, e) {
      Object.defineProperty(t, "name", { value: e, configurable: true });
    }
    __name(re, "re");
    u();
    c();
    m();
    p();
    d();
    l();
    var ri = /* @__PURE__ */ new Set();
    var ct = /* @__PURE__ */ __name((t, e, ...r) => {
      ri.has(t) || (ri.add(t), Ur(e, ...r));
    }, "ct");
    var D = class t extends Error {
      static {
        __name(this, "t");
      }
      clientVersion;
      errorCode;
      retryable;
      constructor(e, r, n) {
        super(e), this.name = "PrismaClientInitializationError", this.clientVersion = r, this.errorCode = n, Error.captureStackTrace(t);
      }
      get [Symbol.toStringTag]() {
        return "PrismaClientInitializationError";
      }
    };
    re(D, "PrismaClientInitializationError");
    u();
    c();
    m();
    p();
    d();
    l();
    var Z = class extends Error {
      static {
        __name(this, "Z");
      }
      code;
      meta;
      clientVersion;
      batchRequestIdx;
      constructor(e, { code: r, clientVersion: n, meta: i, batchRequestIdx: o }) {
        super(e), this.name = "PrismaClientKnownRequestError", this.code = r, this.clientVersion = n, this.meta = i, Object.defineProperty(this, "batchRequestIdx", { value: o, enumerable: false, writable: true });
      }
      get [Symbol.toStringTag]() {
        return "PrismaClientKnownRequestError";
      }
    };
    re(Z, "PrismaClientKnownRequestError");
    u();
    c();
    m();
    p();
    d();
    l();
    var xe = class extends Error {
      static {
        __name(this, "xe");
      }
      clientVersion;
      constructor(e, r) {
        super(e), this.name = "PrismaClientRustPanicError", this.clientVersion = r;
      }
      get [Symbol.toStringTag]() {
        return "PrismaClientRustPanicError";
      }
    };
    re(xe, "PrismaClientRustPanicError");
    u();
    c();
    m();
    p();
    d();
    l();
    var Q = class extends Error {
      static {
        __name(this, "Q");
      }
      clientVersion;
      batchRequestIdx;
      constructor(e, { clientVersion: r, batchRequestIdx: n }) {
        super(e), this.name = "PrismaClientUnknownRequestError", this.clientVersion = r, Object.defineProperty(this, "batchRequestIdx", { value: n, writable: true, enumerable: false });
      }
      get [Symbol.toStringTag]() {
        return "PrismaClientUnknownRequestError";
      }
    };
    re(Q, "PrismaClientUnknownRequestError");
    u();
    c();
    m();
    p();
    d();
    l();
    var K = class extends Error {
      static {
        __name(this, "K");
      }
      name = "PrismaClientValidationError";
      clientVersion;
      constructor(e, { clientVersion: r }) {
        super(e), this.clientVersion = r;
      }
      get [Symbol.toStringTag]() {
        return "PrismaClientValidationError";
      }
    };
    re(K, "PrismaClientValidationError");
    u();
    c();
    m();
    p();
    d();
    l();
    l();
    function Ve(t) {
      return t === null ? t : Array.isArray(t) ? t.map(Ve) : typeof t == "object" ? ra(t) ? na(t) : t.constructor !== null && t.constructor.name !== "Object" ? t : $e(t, Ve) : t;
    }
    __name(Ve, "Ve");
    function ra(t) {
      return t !== null && typeof t == "object" && typeof t.$type == "string";
    }
    __name(ra, "ra");
    function na({ $type: t, value: e }) {
      switch (t) {
        case "BigInt":
          return BigInt(e);
        case "Bytes": {
          let { buffer: r, byteOffset: n, byteLength: i } = b.from(e, "base64");
          return new Uint8Array(r, n, i);
        }
        case "DateTime":
          return new Date(e);
        case "Decimal":
          return new pe(e);
        case "Json":
          return JSON.parse(e);
        default:
          Ee(e, "Unknown tagged value");
      }
    }
    __name(na, "na");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    var de = class {
      static {
        __name(this, "de");
      }
      _map = /* @__PURE__ */ new Map();
      get(e) {
        return this._map.get(e)?.value;
      }
      set(e, r) {
        this._map.set(e, { value: r });
      }
      getOrCreate(e, r) {
        let n = this._map.get(e);
        if (n) return n.value;
        let i = r();
        return this.set(e, i), i;
      }
    };
    u();
    c();
    m();
    p();
    d();
    l();
    function Te(t) {
      return t.substring(0, 1).toLowerCase() + t.substring(1);
    }
    __name(Te, "Te");
    u();
    c();
    m();
    p();
    d();
    l();
    function ii(t, e) {
      let r = {};
      for (let n of t) {
        let i = n[e];
        r[i] = n;
      }
      return r;
    }
    __name(ii, "ii");
    u();
    c();
    m();
    p();
    d();
    l();
    function mt(t) {
      let e;
      return { get() {
        return e || (e = { value: t() }), e.value;
      } };
    }
    __name(mt, "mt");
    u();
    c();
    m();
    p();
    d();
    l();
    function oi(t) {
      return { models: Br(t.models), enums: Br(t.enums), types: Br(t.types) };
    }
    __name(oi, "oi");
    function Br(t) {
      let e = {};
      for (let { name: r, ...n } of t) e[r] = n;
      return e;
    }
    __name(Br, "Br");
    u();
    c();
    m();
    p();
    d();
    l();
    function je(t) {
      return t instanceof Date || Object.prototype.toString.call(t) === "[object Date]";
    }
    __name(je, "je");
    function Vt(t) {
      return t.toString() !== "Invalid Date";
    }
    __name(Vt, "Vt");
    u();
    c();
    m();
    p();
    d();
    l();
    l();
    function Qe(t) {
      return T.isDecimal(t) ? true : t !== null && typeof t == "object" && typeof t.s == "number" && typeof t.e == "number" && typeof t.toFixed == "function" && Array.isArray(t.d);
    }
    __name(Qe, "Qe");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    var dt = {};
    it(dt, { ModelAction: /* @__PURE__ */ __name(() => pt, "ModelAction"), datamodelEnumToSchemaEnum: /* @__PURE__ */ __name(() => ia, "datamodelEnumToSchemaEnum") });
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    function ia(t) {
      return { name: t.name, values: t.values.map((e) => e.name) };
    }
    __name(ia, "ia");
    u();
    c();
    m();
    p();
    d();
    l();
    var pt = ((F) => (F.findUnique = "findUnique", F.findUniqueOrThrow = "findUniqueOrThrow", F.findFirst = "findFirst", F.findFirstOrThrow = "findFirstOrThrow", F.findMany = "findMany", F.create = "create", F.createMany = "createMany", F.createManyAndReturn = "createManyAndReturn", F.update = "update", F.updateMany = "updateMany", F.updateManyAndReturn = "updateManyAndReturn", F.upsert = "upsert", F.delete = "delete", F.deleteMany = "deleteMany", F.groupBy = "groupBy", F.count = "count", F.aggregate = "aggregate", F.findRaw = "findRaw", F.aggregateRaw = "aggregateRaw", F))(pt || {});
    var oa = ot(zn());
    var sa = { red: qe, gray: Ln, dim: Ft, bold: Lt, underline: On, highlightSource: /* @__PURE__ */ __name((t) => t.highlight(), "highlightSource") };
    var aa = { red: /* @__PURE__ */ __name((t) => t, "red"), gray: /* @__PURE__ */ __name((t) => t, "gray"), dim: /* @__PURE__ */ __name((t) => t, "dim"), bold: /* @__PURE__ */ __name((t) => t, "bold"), underline: /* @__PURE__ */ __name((t) => t, "underline"), highlightSource: /* @__PURE__ */ __name((t) => t, "highlightSource") };
    function la({ message: t, originalMethod: e, isPanic: r, callArguments: n }) {
      return { functionName: `prisma.${e}()`, message: t, isPanic: r ?? false, callArguments: n };
    }
    __name(la, "la");
    function ua({ functionName: t, location: e, message: r, isPanic: n, contextLines: i, callArguments: o }, s) {
      let a = [""], f = e ? " in" : ":";
      if (n ? (a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)), a.push(s.red(`It occurred in the ${s.bold(`\`${t}\``)} invocation${f}`))) : a.push(s.red(`Invalid ${s.bold(`\`${t}\``)} invocation${f}`)), e && a.push(s.underline(ca(e))), i) {
        a.push("");
        let y = [i.toString()];
        o && (y.push(o), y.push(s.dim(")"))), a.push(y.join("")), o && a.push("");
      } else a.push(""), o && a.push(o), a.push("");
      return a.push(r), a.join(`
`);
    }
    __name(ua, "ua");
    function ca(t) {
      let e = [t.fileName];
      return t.lineNumber && e.push(String(t.lineNumber)), t.columnNumber && e.push(String(t.columnNumber)), e.join(":");
    }
    __name(ca, "ca");
    function jt(t) {
      let e = t.showColors ? sa : aa, r;
      return typeof $getTemplateParameters < "u" ? r = $getTemplateParameters(t, e) : r = la(t), ua(r, e);
    }
    __name(jt, "jt");
    u();
    c();
    m();
    p();
    d();
    l();
    var fi = ot($r());
    u();
    c();
    m();
    p();
    d();
    l();
    function ui(t, e, r) {
      let n = ci(t), i = ma(n), o = da(i);
      o ? Qt(o, e, r) : e.addErrorMessage(() => "Unknown error");
    }
    __name(ui, "ui");
    function ci(t) {
      return t.errors.flatMap((e) => e.kind === "Union" ? ci(e) : [e]);
    }
    __name(ci, "ci");
    function ma(t) {
      let e = /* @__PURE__ */ new Map(), r = [];
      for (let n of t) {
        if (n.kind !== "InvalidArgumentType") {
          r.push(n);
          continue;
        }
        let i = `${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`, o = e.get(i);
        o ? e.set(i, { ...n, argument: { ...n.argument, typeNames: pa(o.argument.typeNames, n.argument.typeNames) } }) : e.set(i, n);
      }
      return r.push(...e.values()), r;
    }
    __name(ma, "ma");
    function pa(t, e) {
      return [...new Set(t.concat(e))];
    }
    __name(pa, "pa");
    function da(t) {
      return qr(t, (e, r) => {
        let n = ai(e), i = ai(r);
        return n !== i ? n - i : li(e) - li(r);
      });
    }
    __name(da, "da");
    function ai(t) {
      let e = 0;
      return Array.isArray(t.selectionPath) && (e += t.selectionPath.length), Array.isArray(t.argumentPath) && (e += t.argumentPath.length), e;
    }
    __name(ai, "ai");
    function li(t) {
      switch (t.kind) {
        case "InvalidArgumentValue":
        case "ValueTooLarge":
          return 20;
        case "InvalidArgumentType":
          return 10;
        case "RequiredArgumentMissing":
          return -10;
        default:
          return 0;
      }
    }
    __name(li, "li");
    u();
    c();
    m();
    p();
    d();
    l();
    var ne = class {
      static {
        __name(this, "ne");
      }
      constructor(e, r) {
        this.name = e;
        this.value = r;
      }
      isRequired = false;
      makeRequired() {
        return this.isRequired = true, this;
      }
      write(e) {
        let { colors: { green: r } } = e.context;
        e.addMarginSymbol(r(this.isRequired ? "+" : "?")), e.write(r(this.name)), this.isRequired || e.write(r("?")), e.write(r(": ")), typeof this.value == "string" ? e.write(r(this.value)) : e.write(this.value);
      }
    };
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    pi();
    u();
    c();
    m();
    p();
    d();
    l();
    var Je = class {
      static {
        __name(this, "Je");
      }
      constructor(e = 0, r) {
        this.context = r;
        this.currentIndent = e;
      }
      lines = [];
      currentLine = "";
      currentIndent = 0;
      marginSymbol;
      afterNextNewLineCallback;
      write(e) {
        return typeof e == "string" ? this.currentLine += e : e.write(this), this;
      }
      writeJoined(e, r, n = (i, o) => o.write(i)) {
        let i = r.length - 1;
        for (let o = 0; o < r.length; o++) n(r[o], this), o !== i && this.write(e);
        return this;
      }
      writeLine(e) {
        return this.write(e).newLine();
      }
      newLine() {
        this.lines.push(this.indentedCurrentLine()), this.currentLine = "", this.marginSymbol = void 0;
        let e = this.afterNextNewLineCallback;
        return this.afterNextNewLineCallback = void 0, e?.(), this;
      }
      withIndent(e) {
        return this.indent(), e(this), this.unindent(), this;
      }
      afterNextNewline(e) {
        return this.afterNextNewLineCallback = e, this;
      }
      indent() {
        return this.currentIndent++, this;
      }
      unindent() {
        return this.currentIndent > 0 && this.currentIndent--, this;
      }
      addMarginSymbol(e) {
        return this.marginSymbol = e, this;
      }
      toString() {
        return this.lines.concat(this.indentedCurrentLine()).join(`
`);
      }
      getCurrentLineLength() {
        return this.currentLine.length;
      }
      indentedCurrentLine() {
        let e = this.currentLine.padStart(this.currentLine.length + 2 * this.currentIndent);
        return this.marginSymbol ? this.marginSymbol + e.slice(1) : e;
      }
    };
    mi();
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    var Jt = class {
      static {
        __name(this, "Jt");
      }
      constructor(e) {
        this.value = e;
      }
      write(e) {
        e.write(this.value);
      }
      markAsError() {
        this.value.markAsError();
      }
    };
    u();
    c();
    m();
    p();
    d();
    l();
    var Gt = /* @__PURE__ */ __name((t) => t, "Gt");
    var Wt = { bold: Gt, red: Gt, green: Gt, dim: Gt, enabled: false };
    var di = { bold: Lt, red: qe, green: In, dim: Ft, enabled: true };
    var Ge = { write(t) {
      t.writeLine(",");
    } };
    u();
    c();
    m();
    p();
    d();
    l();
    var fe = class {
      static {
        __name(this, "fe");
      }
      constructor(e) {
        this.contents = e;
      }
      isUnderlined = false;
      color = /* @__PURE__ */ __name((e) => e, "color");
      underline() {
        return this.isUnderlined = true, this;
      }
      setColor(e) {
        return this.color = e, this;
      }
      write(e) {
        let r = e.getCurrentLineLength();
        e.write(this.color(this.contents)), this.isUnderlined && e.afterNextNewline(() => {
          e.write(" ".repeat(r)).writeLine(this.color("~".repeat(this.contents.length)));
        });
      }
    };
    u();
    c();
    m();
    p();
    d();
    l();
    var Ce = class {
      static {
        __name(this, "Ce");
      }
      hasError = false;
      markAsError() {
        return this.hasError = true, this;
      }
    };
    var We = class extends Ce {
      static {
        __name(this, "We");
      }
      items = [];
      addItem(e) {
        return this.items.push(new Jt(e)), this;
      }
      getField(e) {
        return this.items[e];
      }
      getPrintWidth() {
        return this.items.length === 0 ? 2 : Math.max(...this.items.map((r) => r.value.getPrintWidth())) + 2;
      }
      write(e) {
        if (this.items.length === 0) {
          this.writeEmpty(e);
          return;
        }
        this.writeWithItems(e);
      }
      writeEmpty(e) {
        let r = new fe("[]");
        this.hasError && r.setColor(e.context.colors.red).underline(), e.write(r);
      }
      writeWithItems(e) {
        let { colors: r } = e.context;
        e.writeLine("[").withIndent(() => e.writeJoined(Ge, this.items).newLine()).write("]"), this.hasError && e.afterNextNewline(() => {
          e.writeLine(r.red("~".repeat(this.getPrintWidth())));
        });
      }
      asObject() {
      }
    };
    var Ke = class t extends Ce {
      static {
        __name(this, "t");
      }
      fields = {};
      suggestions = [];
      addField(e) {
        this.fields[e.name] = e;
      }
      addSuggestion(e) {
        this.suggestions.push(e);
      }
      getField(e) {
        return this.fields[e];
      }
      getDeepField(e) {
        let [r, ...n] = e, i = this.getField(r);
        if (!i) return;
        let o = i;
        for (let s of n) {
          let a;
          if (o.value instanceof t ? a = o.value.getField(s) : o.value instanceof We && (a = o.value.getField(Number(s))), !a) return;
          o = a;
        }
        return o;
      }
      getDeepFieldValue(e) {
        return e.length === 0 ? this : this.getDeepField(e)?.value;
      }
      hasField(e) {
        return !!this.getField(e);
      }
      removeAllFields() {
        this.fields = {};
      }
      removeField(e) {
        delete this.fields[e];
      }
      getFields() {
        return this.fields;
      }
      isEmpty() {
        return Object.keys(this.fields).length === 0;
      }
      getFieldValue(e) {
        return this.getField(e)?.value;
      }
      getDeepSubSelectionValue(e) {
        let r = this;
        for (let n of e) {
          if (!(r instanceof t)) return;
          let i = r.getSubSelectionValue(n);
          if (!i) return;
          r = i;
        }
        return r;
      }
      getDeepSelectionParent(e) {
        let r = this.getSelectionParent();
        if (!r) return;
        let n = r;
        for (let i of e) {
          let o = n.value.getFieldValue(i);
          if (!o || !(o instanceof t)) return;
          let s = o.getSelectionParent();
          if (!s) return;
          n = s;
        }
        return n;
      }
      getSelectionParent() {
        let e = this.getField("select")?.value.asObject();
        if (e) return { kind: "select", value: e };
        let r = this.getField("include")?.value.asObject();
        if (r) return { kind: "include", value: r };
      }
      getSubSelectionValue(e) {
        return this.getSelectionParent()?.value.fields[e].value;
      }
      getPrintWidth() {
        let e = Object.values(this.fields);
        return e.length == 0 ? 2 : Math.max(...e.map((n) => n.getPrintWidth())) + 2;
      }
      write(e) {
        let r = Object.values(this.fields);
        if (r.length === 0 && this.suggestions.length === 0) {
          this.writeEmpty(e);
          return;
        }
        this.writeWithContents(e, r);
      }
      asObject() {
        return this;
      }
      writeEmpty(e) {
        let r = new fe("{}");
        this.hasError && r.setColor(e.context.colors.red).underline(), e.write(r);
      }
      writeWithContents(e, r) {
        e.writeLine("{").withIndent(() => {
          e.writeJoined(Ge, [...r, ...this.suggestions]).newLine();
        }), e.write("}"), this.hasError && e.afterNextNewline(() => {
          e.writeLine(e.context.colors.red("~".repeat(this.getPrintWidth())));
        });
      }
    };
    u();
    c();
    m();
    p();
    d();
    l();
    var W = class extends Ce {
      static {
        __name(this, "W");
      }
      constructor(r) {
        super();
        this.text = r;
      }
      getPrintWidth() {
        return this.text.length;
      }
      write(r) {
        let n = new fe(this.text);
        this.hasError && n.underline().setColor(r.context.colors.red), r.write(n);
      }
      asObject() {
      }
    };
    u();
    c();
    m();
    p();
    d();
    l();
    var ft = class {
      static {
        __name(this, "ft");
      }
      fields = [];
      addField(e, r) {
        return this.fields.push({ write(n) {
          let { green: i, dim: o } = n.context.colors;
          n.write(i(o(`${e}: ${r}`))).addMarginSymbol(i(o("+")));
        } }), this;
      }
      write(e) {
        let { colors: { green: r } } = e.context;
        e.writeLine(r("{")).withIndent(() => {
          e.writeJoined(Ge, this.fields).newLine();
        }).write(r("}")).addMarginSymbol(r("+"));
      }
    };
    function Qt(t, e, r) {
      switch (t.kind) {
        case "MutuallyExclusiveFields":
          fa(t, e);
          break;
        case "IncludeOnScalar":
          ga(t, e);
          break;
        case "EmptySelection":
          ya(t, e, r);
          break;
        case "UnknownSelectionField":
          Ea(t, e);
          break;
        case "InvalidSelectionValue":
          xa(t, e);
          break;
        case "UnknownArgument":
          Pa(t, e);
          break;
        case "UnknownInputField":
          va(t, e);
          break;
        case "RequiredArgumentMissing":
          Ta(t, e);
          break;
        case "InvalidArgumentType":
          Ca(t, e);
          break;
        case "InvalidArgumentValue":
          Ra(t, e);
          break;
        case "ValueTooLarge":
          Aa(t, e);
          break;
        case "SomeFieldsMissing":
          Sa(t, e);
          break;
        case "TooManyFieldsGiven":
          ka(t, e);
          break;
        case "Union":
          ui(t, e, r);
          break;
        default:
          throw new Error("not implemented: " + t.kind);
      }
    }
    __name(Qt, "Qt");
    function fa(t, e) {
      let r = e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();
      r && (r.getField(t.firstField)?.markAsError(), r.getField(t.secondField)?.markAsError()), e.addErrorMessage((n) => `Please ${n.bold("either")} use ${n.green(`\`${t.firstField}\``)} or ${n.green(`\`${t.secondField}\``)}, but ${n.red("not both")} at the same time.`);
    }
    __name(fa, "fa");
    function ga(t, e) {
      let [r, n] = He(t.selectionPath), i = t.outputType, o = e.arguments.getDeepSelectionParent(r)?.value;
      if (o && (o.getField(n)?.markAsError(), i)) for (let s of i.fields) s.isRelation && o.addSuggestion(new ne(s.name, "true"));
      e.addErrorMessage((s) => {
        let a = `Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;
        return i ? a += ` on model ${s.bold(i.name)}. ${gt(s)}` : a += ".", a += `
Note that ${s.bold("include")} statements only accept relation fields.`, a;
      });
    }
    __name(ga, "ga");
    function ya(t, e, r) {
      let n = e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();
      if (n) {
        let i = n.getField("omit")?.value.asObject();
        if (i) {
          ha(t, e, i);
          return;
        }
        if (n.hasField("select")) {
          ba(t, e);
          return;
        }
      }
      if (r?.[Te(t.outputType.name)]) {
        wa(t, e);
        return;
      }
      e.addErrorMessage(() => `Unknown field at "${t.selectionPath.join(".")} selection"`);
    }
    __name(ya, "ya");
    function ha(t, e, r) {
      r.removeAllFields();
      for (let n of t.outputType.fields) r.addSuggestion(new ne(n.name, "false"));
      e.addErrorMessage((n) => `The ${n.red("omit")} statement includes every field of the model ${n.bold(t.outputType.name)}. At least one field must be included in the result`);
    }
    __name(ha, "ha");
    function ba(t, e) {
      let r = t.outputType, n = e.arguments.getDeepSelectionParent(t.selectionPath)?.value, i = n?.isEmpty() ?? false;
      n && (n.removeAllFields(), hi(n, r)), e.addErrorMessage((o) => i ? `The ${o.red("`select`")} statement for type ${o.bold(r.name)} must not be empty. ${gt(o)}` : `The ${o.red("`select`")} statement for type ${o.bold(r.name)} needs ${o.bold("at least one truthy value")}.`);
    }
    __name(ba, "ba");
    function wa(t, e) {
      let r = new ft();
      for (let i of t.outputType.fields) i.isRelation || r.addField(i.name, "false");
      let n = new ne("omit", r).makeRequired();
      if (t.selectionPath.length === 0) e.arguments.addSuggestion(n);
      else {
        let [i, o] = He(t.selectionPath), a = e.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(o);
        if (a) {
          let f = a?.value.asObject() ?? new Ke();
          f.addSuggestion(n), a.value = f;
        }
      }
      e.addErrorMessage((i) => `The global ${i.red("omit")} configuration excludes every field of the model ${i.bold(t.outputType.name)}. At least one field must be included in the result`);
    }
    __name(wa, "wa");
    function Ea(t, e) {
      let r = bi(t.selectionPath, e);
      if (r.parentKind !== "unknown") {
        r.field.markAsError();
        let n = r.parent;
        switch (r.parentKind) {
          case "select":
            hi(n, t.outputType);
            break;
          case "include":
            Oa(n, t.outputType);
            break;
          case "omit":
            Ia(n, t.outputType);
            break;
        }
      }
      e.addErrorMessage((n) => {
        let i = [`Unknown field ${n.red(`\`${r.fieldName}\``)}`];
        return r.parentKind !== "unknown" && i.push(`for ${n.bold(r.parentKind)} statement`), i.push(`on model ${n.bold(`\`${t.outputType.name}\``)}.`), i.push(gt(n)), i.join(" ");
      });
    }
    __name(Ea, "Ea");
    function xa(t, e) {
      let r = bi(t.selectionPath, e);
      r.parentKind !== "unknown" && r.field.value.markAsError(), e.addErrorMessage((n) => `Invalid value for selection field \`${n.red(r.fieldName)}\`: ${t.underlyingError}`);
    }
    __name(xa, "xa");
    function Pa(t, e) {
      let r = t.argumentPath[0], n = e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();
      n && (n.getField(r)?.markAsError(), Ma(n, t.arguments)), e.addErrorMessage((i) => gi(i, r, t.arguments.map((o) => o.name)));
    }
    __name(Pa, "Pa");
    function va(t, e) {
      let [r, n] = He(t.argumentPath), i = e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();
      if (i) {
        i.getDeepField(t.argumentPath)?.markAsError();
        let o = i.getDeepFieldValue(r)?.asObject();
        o && wi(o, t.inputType);
      }
      e.addErrorMessage((o) => gi(o, n, t.inputType.fields.map((s) => s.name)));
    }
    __name(va, "va");
    function gi(t, e, r) {
      let n = [`Unknown argument \`${t.red(e)}\`.`], i = _a(e, r);
      return i && n.push(`Did you mean \`${t.green(i)}\`?`), r.length > 0 && n.push(gt(t)), n.join(" ");
    }
    __name(gi, "gi");
    function Ta(t, e) {
      let r;
      e.addErrorMessage((f) => r?.value instanceof W && r.value.text === "null" ? `Argument \`${f.green(o)}\` must not be ${f.red("null")}.` : `Argument \`${f.green(o)}\` is missing.`);
      let n = e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();
      if (!n) return;
      let [i, o] = He(t.argumentPath), s = new ft(), a = n.getDeepFieldValue(i)?.asObject();
      if (a) {
        if (r = a.getField(o), r && a.removeField(o), t.inputTypes.length === 1 && t.inputTypes[0].kind === "object") {
          for (let f of t.inputTypes[0].fields) s.addField(f.name, f.typeNames.join(" | "));
          a.addSuggestion(new ne(o, s).makeRequired());
        } else {
          let f = t.inputTypes.map(yi).join(" | ");
          a.addSuggestion(new ne(o, f).makeRequired());
        }
        if (t.dependentArgumentPath) {
          n.getDeepField(t.dependentArgumentPath)?.markAsError();
          let [, f] = He(t.dependentArgumentPath);
          e.addErrorMessage((y) => `Argument \`${y.green(o)}\` is required because argument \`${y.green(f)}\` was provided.`);
        }
      }
    }
    __name(Ta, "Ta");
    function yi(t) {
      return t.kind === "list" ? `${yi(t.elementType)}[]` : t.name;
    }
    __name(yi, "yi");
    function Ca(t, e) {
      let r = t.argument.name, n = e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();
      n && n.getDeepFieldValue(t.argumentPath)?.markAsError(), e.addErrorMessage((i) => {
        let o = Kt("or", t.argument.typeNames.map((s) => i.green(s)));
        return `Argument \`${i.bold(r)}\`: Invalid value provided. Expected ${o}, provided ${i.red(t.inferredType)}.`;
      });
    }
    __name(Ca, "Ca");
    function Ra(t, e) {
      let r = t.argument.name, n = e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();
      n && n.getDeepFieldValue(t.argumentPath)?.markAsError(), e.addErrorMessage((i) => {
        let o = [`Invalid value for argument \`${i.bold(r)}\``];
        if (t.underlyingError && o.push(`: ${t.underlyingError}`), o.push("."), t.argument.typeNames.length > 0) {
          let s = Kt("or", t.argument.typeNames.map((a) => i.green(a)));
          o.push(` Expected ${s}.`);
        }
        return o.join("");
      });
    }
    __name(Ra, "Ra");
    function Aa(t, e) {
      let r = t.argument.name, n = e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject(), i;
      if (n) {
        let s = n.getDeepField(t.argumentPath)?.value;
        s?.markAsError(), s instanceof W && (i = s.text);
      }
      e.addErrorMessage((o) => {
        let s = ["Unable to fit value"];
        return i && s.push(o.red(i)), s.push(`into a 64-bit signed integer for field \`${o.bold(r)}\``), s.join(" ");
      });
    }
    __name(Aa, "Aa");
    function Sa(t, e) {
      let r = t.argumentPath[t.argumentPath.length - 1], n = e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject();
      if (n) {
        let i = n.getDeepFieldValue(t.argumentPath)?.asObject();
        i && wi(i, t.inputType);
      }
      e.addErrorMessage((i) => {
        let o = [`Argument \`${i.bold(r)}\` of type ${i.bold(t.inputType.name)} needs`];
        return t.constraints.minFieldCount === 1 ? t.constraints.requiredFields ? o.push(`${i.green("at least one of")} ${Kt("or", t.constraints.requiredFields.map((s) => `\`${i.bold(s)}\``))} arguments.`) : o.push(`${i.green("at least one")} argument.`) : o.push(`${i.green(`at least ${t.constraints.minFieldCount}`)} arguments.`), o.push(gt(i)), o.join(" ");
      });
    }
    __name(Sa, "Sa");
    function ka(t, e) {
      let r = t.argumentPath[t.argumentPath.length - 1], n = e.arguments.getDeepSubSelectionValue(t.selectionPath)?.asObject(), i = [];
      if (n) {
        let o = n.getDeepFieldValue(t.argumentPath)?.asObject();
        o && (o.markAsError(), i = Object.keys(o.getFields()));
      }
      e.addErrorMessage((o) => {
        let s = [`Argument \`${o.bold(r)}\` of type ${o.bold(t.inputType.name)} needs`];
        return t.constraints.minFieldCount === 1 && t.constraints.maxFieldCount == 1 ? s.push(`${o.green("exactly one")} argument,`) : t.constraints.maxFieldCount == 1 ? s.push(`${o.green("at most one")} argument,`) : s.push(`${o.green(`at most ${t.constraints.maxFieldCount}`)} arguments,`), s.push(`but you provided ${Kt("and", i.map((a) => o.red(a)))}. Please choose`), t.constraints.maxFieldCount === 1 ? s.push("one.") : s.push(`${t.constraints.maxFieldCount}.`), s.join(" ");
      });
    }
    __name(ka, "ka");
    function hi(t, e) {
      for (let r of e.fields) t.hasField(r.name) || t.addSuggestion(new ne(r.name, "true"));
    }
    __name(hi, "hi");
    function Oa(t, e) {
      for (let r of e.fields) r.isRelation && !t.hasField(r.name) && t.addSuggestion(new ne(r.name, "true"));
    }
    __name(Oa, "Oa");
    function Ia(t, e) {
      for (let r of e.fields) !t.hasField(r.name) && !r.isRelation && t.addSuggestion(new ne(r.name, "true"));
    }
    __name(Ia, "Ia");
    function Ma(t, e) {
      for (let r of e) t.hasField(r.name) || t.addSuggestion(new ne(r.name, r.typeNames.join(" | ")));
    }
    __name(Ma, "Ma");
    function bi(t, e) {
      let [r, n] = He(t), i = e.arguments.getDeepSubSelectionValue(r)?.asObject();
      if (!i) return { parentKind: "unknown", fieldName: n };
      let o = i.getFieldValue("select")?.asObject(), s = i.getFieldValue("include")?.asObject(), a = i.getFieldValue("omit")?.asObject(), f = o?.getField(n);
      return o && f ? { parentKind: "select", parent: o, field: f, fieldName: n } : (f = s?.getField(n), s && f ? { parentKind: "include", field: f, parent: s, fieldName: n } : (f = a?.getField(n), a && f ? { parentKind: "omit", field: f, parent: a, fieldName: n } : { parentKind: "unknown", fieldName: n }));
    }
    __name(bi, "bi");
    function wi(t, e) {
      if (e.kind === "object") for (let r of e.fields) t.hasField(r.name) || t.addSuggestion(new ne(r.name, r.typeNames.join(" | ")));
    }
    __name(wi, "wi");
    function He(t) {
      let e = [...t], r = e.pop();
      if (!r) throw new Error("unexpected empty path");
      return [e, r];
    }
    __name(He, "He");
    function gt({ green: t, enabled: e }) {
      return "Available options are " + (e ? `listed in ${t("green")}` : "marked with ?") + ".";
    }
    __name(gt, "gt");
    function Kt(t, e) {
      if (e.length === 1) return e[0];
      let r = [...e], n = r.pop();
      return `${r.join(", ")} ${t} ${n}`;
    }
    __name(Kt, "Kt");
    var Da = 3;
    function _a(t, e) {
      let r = 1 / 0, n;
      for (let i of e) {
        let o = (0, fi.default)(t, i);
        o > Da || o < r && (r = o, n = i);
      }
      return n;
    }
    __name(_a, "_a");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    var yt = class {
      static {
        __name(this, "yt");
      }
      modelName;
      name;
      typeName;
      isList;
      isEnum;
      constructor(e, r, n, i, o) {
        this.modelName = e, this.name = r, this.typeName = n, this.isList = i, this.isEnum = o;
      }
      _toGraphQLInputType() {
        let e = this.isList ? "List" : "", r = this.isEnum ? "Enum" : "";
        return `${e}${r}${this.typeName}FieldRefInput<${this.modelName}>`;
      }
    };
    function ze(t) {
      return t instanceof yt;
    }
    __name(ze, "ze");
    u();
    c();
    m();
    p();
    d();
    l();
    var Ht = Symbol();
    var jr = /* @__PURE__ */ new WeakMap();
    var Pe = class {
      static {
        __name(this, "Pe");
      }
      constructor(e) {
        e === Ht ? jr.set(this, `Prisma.${this._getName()}`) : jr.set(this, `new Prisma.${this._getNamespace()}.${this._getName()}()`);
      }
      _getName() {
        return this.constructor.name;
      }
      toString() {
        return jr.get(this);
      }
    };
    var ht = class extends Pe {
      static {
        __name(this, "ht");
      }
      _getNamespace() {
        return "NullTypes";
      }
    };
    var bt = class extends ht {
      static {
        __name(this, "bt");
      }
      #e;
    };
    Qr(bt, "DbNull");
    var wt = class extends ht {
      static {
        __name(this, "wt");
      }
      #e;
    };
    Qr(wt, "JsonNull");
    var Et = class extends ht {
      static {
        __name(this, "Et");
      }
      #e;
    };
    Qr(Et, "AnyNull");
    var zt = { classes: { DbNull: bt, JsonNull: wt, AnyNull: Et }, instances: { DbNull: new bt(Ht), JsonNull: new wt(Ht), AnyNull: new Et(Ht) } };
    function Qr(t, e) {
      Object.defineProperty(t, "name", { value: e, configurable: true });
    }
    __name(Qr, "Qr");
    u();
    c();
    m();
    p();
    d();
    l();
    var Ei = ": ";
    var Yt = class {
      static {
        __name(this, "Yt");
      }
      constructor(e, r) {
        this.name = e;
        this.value = r;
      }
      hasError = false;
      markAsError() {
        this.hasError = true;
      }
      getPrintWidth() {
        return this.name.length + this.value.getPrintWidth() + Ei.length;
      }
      write(e) {
        let r = new fe(this.name);
        this.hasError && r.underline().setColor(e.context.colors.red), e.write(r).write(Ei).write(this.value);
      }
    };
    var Jr = class {
      static {
        __name(this, "Jr");
      }
      arguments;
      errorMessages = [];
      constructor(e) {
        this.arguments = e;
      }
      write(e) {
        e.write(this.arguments);
      }
      addErrorMessage(e) {
        this.errorMessages.push(e);
      }
      renderAllMessages(e) {
        return this.errorMessages.map((r) => r(e)).join(`
`);
      }
    };
    function Ye(t) {
      return new Jr(xi(t));
    }
    __name(Ye, "Ye");
    function xi(t) {
      let e = new Ke();
      for (let [r, n] of Object.entries(t)) {
        let i = new Yt(r, Pi(n));
        e.addField(i);
      }
      return e;
    }
    __name(xi, "xi");
    function Pi(t) {
      if (typeof t == "string") return new W(JSON.stringify(t));
      if (typeof t == "number" || typeof t == "boolean") return new W(String(t));
      if (typeof t == "bigint") return new W(`${t}n`);
      if (t === null) return new W("null");
      if (t === void 0) return new W("undefined");
      if (Qe(t)) return new W(`new Prisma.Decimal("${t.toFixed()}")`);
      if (t instanceof Uint8Array) return b.isBuffer(t) ? new W(`Buffer.alloc(${t.byteLength})`) : new W(`new Uint8Array(${t.byteLength})`);
      if (t instanceof Date) {
        let e = Vt(t) ? t.toISOString() : "Invalid Date";
        return new W(`new Date("${e}")`);
      }
      return t instanceof Pe ? new W(`Prisma.${t._getName()}`) : ze(t) ? new W(`prisma.${Te(t.modelName)}.$fields.${t.name}`) : Array.isArray(t) ? La(t) : typeof t == "object" ? xi(t) : new W(Object.prototype.toString.call(t));
    }
    __name(Pi, "Pi");
    function La(t) {
      let e = new We();
      for (let r of t) e.addItem(Pi(r));
      return e;
    }
    __name(La, "La");
    function Xt(t, e) {
      let r = e === "pretty" ? di : Wt, n = t.renderAllMessages(r), i = new Je(0, { colors: r }).write(t).toString();
      return { message: n, args: i };
    }
    __name(Xt, "Xt");
    function Zt({ args: t, errors: e, errorFormat: r, callsite: n, originalMethod: i, clientVersion: o, globalOmit: s }) {
      let a = Ye(t);
      for (let R of e) Qt(R, a, s);
      let { message: f, args: y } = Xt(a, r), C = jt({ message: f, callsite: n, originalMethod: i, showColors: r === "pretty", callArguments: y });
      throw new K(C, { clientVersion: o });
    }
    __name(Zt, "Zt");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    function ge(t) {
      return t.replace(/^./, (e) => e.toLowerCase());
    }
    __name(ge, "ge");
    u();
    c();
    m();
    p();
    d();
    l();
    function Ti(t, e, r) {
      let n = ge(r);
      return !e.result || !(e.result.$allModels || e.result[n]) ? t : Fa({ ...t, ...vi(e.name, t, e.result.$allModels), ...vi(e.name, t, e.result[n]) });
    }
    __name(Ti, "Ti");
    function Fa(t) {
      let e = new de(), r = /* @__PURE__ */ __name((n, i) => e.getOrCreate(n, () => i.has(n) ? [n] : (i.add(n), t[n] ? t[n].needs.flatMap((o) => r(o, i)) : [n])), "r");
      return $e(t, (n) => ({ ...n, needs: r(n.name, /* @__PURE__ */ new Set()) }));
    }
    __name(Fa, "Fa");
    function vi(t, e, r) {
      return r ? $e(r, ({ needs: n, compute: i }, o) => ({ name: o, needs: n ? Object.keys(n).filter((s) => n[s]) : [], compute: Ua(e, o, i) })) : {};
    }
    __name(vi, "vi");
    function Ua(t, e, r) {
      let n = t?.[e]?.compute;
      return n ? (i) => r({ ...i, [e]: n(i) }) : r;
    }
    __name(Ua, "Ua");
    function Ci(t, e) {
      if (!e) return t;
      let r = { ...t };
      for (let n of Object.values(e)) if (t[n.name]) for (let i of n.needs) r[i] = true;
      return r;
    }
    __name(Ci, "Ci");
    function Ri(t, e) {
      if (!e) return t;
      let r = { ...t };
      for (let n of Object.values(e)) if (!t[n.name]) for (let i of n.needs) delete r[i];
      return r;
    }
    __name(Ri, "Ri");
    var er = class {
      static {
        __name(this, "er");
      }
      constructor(e, r) {
        this.extension = e;
        this.previous = r;
      }
      computedFieldsCache = new de();
      modelExtensionsCache = new de();
      queryCallbacksCache = new de();
      clientExtensions = mt(() => this.extension.client ? { ...this.previous?.getAllClientExtensions(), ...this.extension.client } : this.previous?.getAllClientExtensions());
      batchCallbacks = mt(() => {
        let e = this.previous?.getAllBatchQueryCallbacks() ?? [], r = this.extension.query?.$__internalBatch;
        return r ? e.concat(r) : e;
      });
      getAllComputedFields(e) {
        return this.computedFieldsCache.getOrCreate(e, () => Ti(this.previous?.getAllComputedFields(e), this.extension, e));
      }
      getAllClientExtensions() {
        return this.clientExtensions.get();
      }
      getAllModelExtensions(e) {
        return this.modelExtensionsCache.getOrCreate(e, () => {
          let r = ge(e);
          return !this.extension.model || !(this.extension.model[r] || this.extension.model.$allModels) ? this.previous?.getAllModelExtensions(e) : { ...this.previous?.getAllModelExtensions(e), ...this.extension.model.$allModels, ...this.extension.model[r] };
        });
      }
      getAllQueryCallbacks(e, r) {
        return this.queryCallbacksCache.getOrCreate(`${e}:${r}`, () => {
          let n = this.previous?.getAllQueryCallbacks(e, r) ?? [], i = [], o = this.extension.query;
          return !o || !(o[e] || o.$allModels || o[r] || o.$allOperations) ? n : (o[e] !== void 0 && (o[e][r] !== void 0 && i.push(o[e][r]), o[e].$allOperations !== void 0 && i.push(o[e].$allOperations)), e !== "$none" && o.$allModels !== void 0 && (o.$allModels[r] !== void 0 && i.push(o.$allModels[r]), o.$allModels.$allOperations !== void 0 && i.push(o.$allModels.$allOperations)), o[r] !== void 0 && i.push(o[r]), o.$allOperations !== void 0 && i.push(o.$allOperations), n.concat(i));
        });
      }
      getAllBatchQueryCallbacks() {
        return this.batchCallbacks.get();
      }
    };
    var Xe = class t {
      static {
        __name(this, "t");
      }
      constructor(e) {
        this.head = e;
      }
      static empty() {
        return new t();
      }
      static single(e) {
        return new t(new er(e));
      }
      isEmpty() {
        return this.head === void 0;
      }
      append(e) {
        return new t(new er(e, this.head));
      }
      getAllComputedFields(e) {
        return this.head?.getAllComputedFields(e);
      }
      getAllClientExtensions() {
        return this.head?.getAllClientExtensions();
      }
      getAllModelExtensions(e) {
        return this.head?.getAllModelExtensions(e);
      }
      getAllQueryCallbacks(e, r) {
        return this.head?.getAllQueryCallbacks(e, r) ?? [];
      }
      getAllBatchQueryCallbacks() {
        return this.head?.getAllBatchQueryCallbacks() ?? [];
      }
    };
    u();
    c();
    m();
    p();
    d();
    l();
    var tr = class {
      static {
        __name(this, "tr");
      }
      constructor(e) {
        this.name = e;
      }
    };
    function Ai(t) {
      return t instanceof tr;
    }
    __name(Ai, "Ai");
    function Si(t) {
      return new tr(t);
    }
    __name(Si, "Si");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    var ki = Symbol();
    var xt = class {
      static {
        __name(this, "xt");
      }
      constructor(e) {
        if (e !== ki) throw new Error("Skip instance can not be constructed directly");
      }
      ifUndefined(e) {
        return e === void 0 ? rr : e;
      }
    };
    var rr = new xt(ki);
    function ye(t) {
      return t instanceof xt;
    }
    __name(ye, "ye");
    var Na = { findUnique: "findUnique", findUniqueOrThrow: "findUniqueOrThrow", findFirst: "findFirst", findFirstOrThrow: "findFirstOrThrow", findMany: "findMany", count: "aggregate", create: "createOne", createMany: "createMany", createManyAndReturn: "createManyAndReturn", update: "updateOne", updateMany: "updateMany", updateManyAndReturn: "updateManyAndReturn", upsert: "upsertOne", delete: "deleteOne", deleteMany: "deleteMany", executeRaw: "executeRaw", queryRaw: "queryRaw", aggregate: "aggregate", groupBy: "groupBy", runCommandRaw: "runCommandRaw", findRaw: "findRaw", aggregateRaw: "aggregateRaw" };
    var Oi = "explicitly `undefined` values are not allowed";
    function nr({ modelName: t, action: e, args: r, runtimeDataModel: n, extensions: i = Xe.empty(), callsite: o, clientMethod: s, errorFormat: a, clientVersion: f, previewFeatures: y, globalOmit: C }) {
      let R = new Gr({ runtimeDataModel: n, modelName: t, action: e, rootArgs: r, callsite: o, extensions: i, selectionPath: [], argumentPath: [], originalMethod: s, errorFormat: a, clientVersion: f, previewFeatures: y, globalOmit: C });
      return { modelName: t, action: Na[e], query: Pt(r, R) };
    }
    __name(nr, "nr");
    function Pt({ select: t, include: e, ...r } = {}, n) {
      let i = r.omit;
      return delete r.omit, { arguments: Mi(r, n), selection: qa(t, e, i, n) };
    }
    __name(Pt, "Pt");
    function qa(t, e, r, n) {
      return t ? (e ? n.throwValidationError({ kind: "MutuallyExclusiveFields", firstField: "include", secondField: "select", selectionPath: n.getSelectionPath() }) : r && n.throwValidationError({ kind: "MutuallyExclusiveFields", firstField: "omit", secondField: "select", selectionPath: n.getSelectionPath() }), ja(t, n)) : Ba(n, e, r);
    }
    __name(qa, "qa");
    function Ba(t, e, r) {
      let n = {};
      return t.modelOrType && !t.isRawAction() && (n.$composites = true, n.$scalars = true), e && $a(n, e, t), Va(n, r, t), n;
    }
    __name(Ba, "Ba");
    function $a(t, e, r) {
      for (let [n, i] of Object.entries(e)) {
        if (ye(i)) continue;
        let o = r.nestSelection(n);
        if (Wr(i, o), i === false || i === void 0) {
          t[n] = false;
          continue;
        }
        let s = r.findField(n);
        if (s && s.kind !== "object" && r.throwValidationError({ kind: "IncludeOnScalar", selectionPath: r.getSelectionPath().concat(n), outputType: r.getOutputTypeDescription() }), s) {
          t[n] = Pt(i === true ? {} : i, o);
          continue;
        }
        if (i === true) {
          t[n] = true;
          continue;
        }
        t[n] = Pt(i, o);
      }
    }
    __name($a, "$a");
    function Va(t, e, r) {
      let n = r.getComputedFields(), i = { ...r.getGlobalOmit(), ...e }, o = Ri(i, n);
      for (let [s, a] of Object.entries(o)) {
        if (ye(a)) continue;
        Wr(a, r.nestSelection(s));
        let f = r.findField(s);
        n?.[s] && !f || (t[s] = !a);
      }
    }
    __name(Va, "Va");
    function ja(t, e) {
      let r = {}, n = e.getComputedFields(), i = Ci(t, n);
      for (let [o, s] of Object.entries(i)) {
        if (ye(s)) continue;
        let a = e.nestSelection(o);
        Wr(s, a);
        let f = e.findField(o);
        if (!(n?.[o] && !f)) {
          if (s === false || s === void 0 || ye(s)) {
            r[o] = false;
            continue;
          }
          if (s === true) {
            f?.kind === "object" ? r[o] = Pt({}, a) : r[o] = true;
            continue;
          }
          r[o] = Pt(s, a);
        }
      }
      return r;
    }
    __name(ja, "ja");
    function Ii(t, e) {
      if (t === null) return null;
      if (typeof t == "string" || typeof t == "number" || typeof t == "boolean") return t;
      if (typeof t == "bigint") return { $type: "BigInt", value: String(t) };
      if (je(t)) {
        if (Vt(t)) return { $type: "DateTime", value: t.toISOString() };
        e.throwValidationError({ kind: "InvalidArgumentValue", selectionPath: e.getSelectionPath(), argumentPath: e.getArgumentPath(), argument: { name: e.getArgumentName(), typeNames: ["Date"] }, underlyingError: "Provided Date object is invalid" });
      }
      if (Ai(t)) return { $type: "Param", value: t.name };
      if (ze(t)) return { $type: "FieldRef", value: { _ref: t.name, _container: t.modelName } };
      if (Array.isArray(t)) return Qa(t, e);
      if (ArrayBuffer.isView(t)) {
        let { buffer: r, byteOffset: n, byteLength: i } = t;
        return { $type: "Bytes", value: b.from(r, n, i).toString("base64") };
      }
      if (Ja(t)) return t.values;
      if (Qe(t)) return { $type: "Decimal", value: t.toFixed() };
      if (t instanceof Pe) {
        if (t !== zt.instances[t._getName()]) throw new Error("Invalid ObjectEnumValue");
        return { $type: "Enum", value: t._getName() };
      }
      if (Ga(t)) return t.toJSON();
      if (typeof t == "object") return Mi(t, e);
      e.throwValidationError({ kind: "InvalidArgumentValue", selectionPath: e.getSelectionPath(), argumentPath: e.getArgumentPath(), argument: { name: e.getArgumentName(), typeNames: [] }, underlyingError: `We could not serialize ${Object.prototype.toString.call(t)} value. Serialize the object to JSON or implement a ".toJSON()" method on it` });
    }
    __name(Ii, "Ii");
    function Mi(t, e) {
      if (t.$type) return { $type: "Raw", value: t };
      let r = {};
      for (let n in t) {
        let i = t[n], o = e.nestArgument(n);
        ye(i) || (i !== void 0 ? r[n] = Ii(i, o) : e.isPreviewFeatureOn("strictUndefinedChecks") && e.throwValidationError({ kind: "InvalidArgumentValue", argumentPath: o.getArgumentPath(), selectionPath: e.getSelectionPath(), argument: { name: e.getArgumentName(), typeNames: [] }, underlyingError: Oi }));
      }
      return r;
    }
    __name(Mi, "Mi");
    function Qa(t, e) {
      let r = [];
      for (let n = 0; n < t.length; n++) {
        let i = e.nestArgument(String(n)), o = t[n];
        if (o === void 0 || ye(o)) {
          let s = o === void 0 ? "undefined" : "Prisma.skip";
          e.throwValidationError({ kind: "InvalidArgumentValue", selectionPath: i.getSelectionPath(), argumentPath: i.getArgumentPath(), argument: { name: `${e.getArgumentName()}[${n}]`, typeNames: [] }, underlyingError: `Can not use \`${s}\` value within array. Use \`null\` or filter out \`${s}\` values` });
        }
        r.push(Ii(o, i));
      }
      return r;
    }
    __name(Qa, "Qa");
    function Ja(t) {
      return typeof t == "object" && t !== null && t.__prismaRawParameters__ === true;
    }
    __name(Ja, "Ja");
    function Ga(t) {
      return typeof t == "object" && t !== null && typeof t.toJSON == "function";
    }
    __name(Ga, "Ga");
    function Wr(t, e) {
      t === void 0 && e.isPreviewFeatureOn("strictUndefinedChecks") && e.throwValidationError({ kind: "InvalidSelectionValue", selectionPath: e.getSelectionPath(), underlyingError: Oi });
    }
    __name(Wr, "Wr");
    var Gr = class t {
      static {
        __name(this, "t");
      }
      constructor(e) {
        this.params = e;
        this.params.modelName && (this.modelOrType = this.params.runtimeDataModel.models[this.params.modelName] ?? this.params.runtimeDataModel.types[this.params.modelName]);
      }
      modelOrType;
      throwValidationError(e) {
        Zt({ errors: [e], originalMethod: this.params.originalMethod, args: this.params.rootArgs ?? {}, callsite: this.params.callsite, errorFormat: this.params.errorFormat, clientVersion: this.params.clientVersion, globalOmit: this.params.globalOmit });
      }
      getSelectionPath() {
        return this.params.selectionPath;
      }
      getArgumentPath() {
        return this.params.argumentPath;
      }
      getArgumentName() {
        return this.params.argumentPath[this.params.argumentPath.length - 1];
      }
      getOutputTypeDescription() {
        if (!(!this.params.modelName || !this.modelOrType)) return { name: this.params.modelName, fields: this.modelOrType.fields.map((e) => ({ name: e.name, typeName: "boolean", isRelation: e.kind === "object" })) };
      }
      isRawAction() {
        return ["executeRaw", "queryRaw", "runCommandRaw", "findRaw", "aggregateRaw"].includes(this.params.action);
      }
      isPreviewFeatureOn(e) {
        return this.params.previewFeatures.includes(e);
      }
      getComputedFields() {
        if (this.params.modelName) return this.params.extensions.getAllComputedFields(this.params.modelName);
      }
      findField(e) {
        return this.modelOrType?.fields.find((r) => r.name === e);
      }
      nestSelection(e) {
        let r = this.findField(e), n = r?.kind === "object" ? r.type : void 0;
        return new t({ ...this.params, modelName: n, selectionPath: this.params.selectionPath.concat(e) });
      }
      getGlobalOmit() {
        return this.params.modelName && this.shouldApplyGlobalOmit() ? this.params.globalOmit?.[Te(this.params.modelName)] ?? {} : {};
      }
      shouldApplyGlobalOmit() {
        switch (this.params.action) {
          case "findFirst":
          case "findFirstOrThrow":
          case "findUniqueOrThrow":
          case "findMany":
          case "upsert":
          case "findUnique":
          case "createManyAndReturn":
          case "create":
          case "update":
          case "updateManyAndReturn":
          case "delete":
            return true;
          case "executeRaw":
          case "aggregateRaw":
          case "runCommandRaw":
          case "findRaw":
          case "createMany":
          case "deleteMany":
          case "groupBy":
          case "updateMany":
          case "count":
          case "aggregate":
          case "queryRaw":
            return false;
          default:
            Ee(this.params.action, "Unknown action");
        }
      }
      nestArgument(e) {
        return new t({ ...this.params, argumentPath: this.params.argumentPath.concat(e) });
      }
    };
    u();
    c();
    m();
    p();
    d();
    l();
    function Di(t) {
      if (!t._hasPreviewFlag("metrics")) throw new K("`metrics` preview feature must be enabled in order to access metrics API", { clientVersion: t._clientVersion });
    }
    __name(Di, "Di");
    var Ze = class {
      static {
        __name(this, "Ze");
      }
      _client;
      constructor(e) {
        this._client = e;
      }
      prometheus(e) {
        return Di(this._client), this._client._engine.metrics({ format: "prometheus", ...e });
      }
      json(e) {
        return Di(this._client), this._client._engine.metrics({ format: "json", ...e });
      }
    };
    u();
    c();
    m();
    p();
    d();
    l();
    function _i(t, e) {
      let r = mt(() => Wa(e));
      Object.defineProperty(t, "dmmf", { get: /* @__PURE__ */ __name(() => r.get(), "get") });
    }
    __name(_i, "_i");
    function Wa(t) {
      throw new Error("Prisma.dmmf is not available when running in edge runtimes.");
    }
    __name(Wa, "Wa");
    u();
    c();
    m();
    p();
    d();
    l();
    var Hr = /* @__PURE__ */ new WeakMap();
    var ir = "$$PrismaTypedSql";
    var vt = class {
      static {
        __name(this, "vt");
      }
      constructor(e, r) {
        Hr.set(this, { sql: e, values: r }), Object.defineProperty(this, ir, { value: ir });
      }
      get sql() {
        return Hr.get(this).sql;
      }
      get values() {
        return Hr.get(this).values;
      }
    };
    function Li(t) {
      return (...e) => new vt(t, e);
    }
    __name(Li, "Li");
    function or(t) {
      return t != null && t[ir] === ir;
    }
    __name(or, "or");
    u();
    c();
    m();
    p();
    d();
    l();
    var Wo = ot(Fi());
    u();
    c();
    m();
    p();
    d();
    l();
    Ui();
    Bn();
    Qn();
    u();
    c();
    m();
    p();
    d();
    l();
    var ee = class t {
      static {
        __name(this, "t");
      }
      constructor(e, r) {
        if (e.length - 1 !== r.length) throw e.length === 0 ? new TypeError("Expected at least 1 string") : new TypeError(`Expected ${e.length} strings to have ${e.length - 1} values`);
        let n = r.reduce((s, a) => s + (a instanceof t ? a.values.length : 1), 0);
        this.values = new Array(n), this.strings = new Array(n + 1), this.strings[0] = e[0];
        let i = 0, o = 0;
        for (; i < r.length; ) {
          let s = r[i++], a = e[i];
          if (s instanceof t) {
            this.strings[o] += s.strings[0];
            let f = 0;
            for (; f < s.values.length; ) this.values[o++] = s.values[f++], this.strings[o] = s.strings[f];
            this.strings[o] += a;
          } else this.values[o++] = s, this.strings[o] = a;
        }
      }
      get sql() {
        let e = this.strings.length, r = 1, n = this.strings[0];
        for (; r < e; ) n += `?${this.strings[r++]}`;
        return n;
      }
      get statement() {
        let e = this.strings.length, r = 1, n = this.strings[0];
        for (; r < e; ) n += `:${r}${this.strings[r++]}`;
        return n;
      }
      get text() {
        let e = this.strings.length, r = 1, n = this.strings[0];
        for (; r < e; ) n += `$${r}${this.strings[r++]}`;
        return n;
      }
      inspect() {
        return { sql: this.sql, statement: this.statement, text: this.text, values: this.values };
      }
    };
    function Ni(t, e = ",", r = "", n = "") {
      if (t.length === 0) throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");
      return new ee([r, ...Array(t.length - 1).fill(e), n], t);
    }
    __name(Ni, "Ni");
    function zr(t) {
      return new ee([t], []);
    }
    __name(zr, "zr");
    var qi = zr("");
    function Yr(t, ...e) {
      return new ee(t, e);
    }
    __name(Yr, "Yr");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    function Tt(t) {
      return { getKeys() {
        return Object.keys(t);
      }, getPropertyValue(e) {
        return t[e];
      } };
    }
    __name(Tt, "Tt");
    u();
    c();
    m();
    p();
    d();
    l();
    function H(t, e) {
      return { getKeys() {
        return [t];
      }, getPropertyValue() {
        return e();
      } };
    }
    __name(H, "H");
    u();
    c();
    m();
    p();
    d();
    l();
    function _e(t) {
      let e = new de();
      return { getKeys() {
        return t.getKeys();
      }, getPropertyValue(r) {
        return e.getOrCreate(r, () => t.getPropertyValue(r));
      }, getPropertyDescriptor(r) {
        return t.getPropertyDescriptor?.(r);
      } };
    }
    __name(_e, "_e");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    var ar = { enumerable: true, configurable: true, writable: true };
    function lr(t) {
      let e = new Set(t);
      return { getPrototypeOf: /* @__PURE__ */ __name(() => Object.prototype, "getPrototypeOf"), getOwnPropertyDescriptor: /* @__PURE__ */ __name(() => ar, "getOwnPropertyDescriptor"), has: /* @__PURE__ */ __name((r, n) => e.has(n), "has"), set: /* @__PURE__ */ __name((r, n, i) => e.add(n) && Reflect.set(r, n, i), "set"), ownKeys: /* @__PURE__ */ __name(() => [...e], "ownKeys") };
    }
    __name(lr, "lr");
    var Bi = Symbol.for("nodejs.util.inspect.custom");
    function le(t, e) {
      let r = Ha(e), n = /* @__PURE__ */ new Set(), i = new Proxy(t, { get(o, s) {
        if (n.has(s)) return o[s];
        let a = r.get(s);
        return a ? a.getPropertyValue(s) : o[s];
      }, has(o, s) {
        if (n.has(s)) return true;
        let a = r.get(s);
        return a ? a.has?.(s) ?? true : Reflect.has(o, s);
      }, ownKeys(o) {
        let s = $i(Reflect.ownKeys(o), r), a = $i(Array.from(r.keys()), r);
        return [.../* @__PURE__ */ new Set([...s, ...a, ...n])];
      }, set(o, s, a) {
        return r.get(s)?.getPropertyDescriptor?.(s)?.writable === false ? false : (n.add(s), Reflect.set(o, s, a));
      }, getOwnPropertyDescriptor(o, s) {
        let a = Reflect.getOwnPropertyDescriptor(o, s);
        if (a && !a.configurable) return a;
        let f = r.get(s);
        return f ? f.getPropertyDescriptor ? { ...ar, ...f?.getPropertyDescriptor(s) } : ar : a;
      }, defineProperty(o, s, a) {
        return n.add(s), Reflect.defineProperty(o, s, a);
      }, getPrototypeOf: /* @__PURE__ */ __name(() => Object.prototype, "getPrototypeOf") });
      return i[Bi] = function() {
        let o = { ...this };
        return delete o[Bi], o;
      }, i;
    }
    __name(le, "le");
    function Ha(t) {
      let e = /* @__PURE__ */ new Map();
      for (let r of t) {
        let n = r.getKeys();
        for (let i of n) e.set(i, r);
      }
      return e;
    }
    __name(Ha, "Ha");
    function $i(t, e) {
      return t.filter((r) => e.get(r)?.has?.(r) ?? true);
    }
    __name($i, "$i");
    u();
    c();
    m();
    p();
    d();
    l();
    function et(t) {
      return { getKeys() {
        return t;
      }, has() {
        return false;
      }, getPropertyValue() {
      } };
    }
    __name(et, "et");
    u();
    c();
    m();
    p();
    d();
    l();
    function ur(t, e) {
      return { batch: t, transaction: e?.kind === "batch" ? { isolationLevel: e.options.isolationLevel } : void 0 };
    }
    __name(ur, "ur");
    u();
    c();
    m();
    p();
    d();
    l();
    function Vi(t) {
      if (t === void 0) return "";
      let e = Ye(t);
      return new Je(0, { colors: Wt }).write(e).toString();
    }
    __name(Vi, "Vi");
    u();
    c();
    m();
    p();
    d();
    l();
    var za = "P2037";
    function cr({ error: t, user_facing_error: e }, r, n) {
      return e.error_code ? new Z(Ya(e, n), { code: e.error_code, clientVersion: r, meta: e.meta, batchRequestIdx: e.batch_request_idx }) : new Q(t, { clientVersion: r, batchRequestIdx: e.batch_request_idx });
    }
    __name(cr, "cr");
    function Ya(t, e) {
      let r = t.message;
      return (e === "postgresql" || e === "postgres" || e === "mysql") && t.error_code === za && (r += `
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`), r;
    }
    __name(Ya, "Ya");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    var Xr = class {
      static {
        __name(this, "Xr");
      }
      getLocation() {
        return null;
      }
    };
    function Re(t) {
      return typeof $EnabledCallSite == "function" && t !== "minimal" ? new $EnabledCallSite() : new Xr();
    }
    __name(Re, "Re");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    var ji = { _avg: true, _count: true, _sum: true, _min: true, _max: true };
    function tt(t = {}) {
      let e = Za(t);
      return Object.entries(e).reduce((n, [i, o]) => (ji[i] !== void 0 ? n.select[i] = { select: o } : n[i] = o, n), { select: {} });
    }
    __name(tt, "tt");
    function Za(t = {}) {
      return typeof t._count == "boolean" ? { ...t, _count: { _all: t._count } } : t;
    }
    __name(Za, "Za");
    function mr(t = {}) {
      return (e) => (typeof t._count == "boolean" && (e._count = e._count._all), e);
    }
    __name(mr, "mr");
    function Qi(t, e) {
      let r = mr(t);
      return e({ action: "aggregate", unpacker: r, argsMapper: tt })(t);
    }
    __name(Qi, "Qi");
    u();
    c();
    m();
    p();
    d();
    l();
    function el(t = {}) {
      let { select: e, ...r } = t;
      return typeof e == "object" ? tt({ ...r, _count: e }) : tt({ ...r, _count: { _all: true } });
    }
    __name(el, "el");
    function tl(t = {}) {
      return typeof t.select == "object" ? (e) => mr(t)(e)._count : (e) => mr(t)(e)._count._all;
    }
    __name(tl, "tl");
    function Ji(t, e) {
      return e({ action: "count", unpacker: tl(t), argsMapper: el })(t);
    }
    __name(Ji, "Ji");
    u();
    c();
    m();
    p();
    d();
    l();
    function rl(t = {}) {
      let e = tt(t);
      if (Array.isArray(e.by)) for (let r of e.by) typeof r == "string" && (e.select[r] = true);
      else typeof e.by == "string" && (e.select[e.by] = true);
      return e;
    }
    __name(rl, "rl");
    function nl(t = {}) {
      return (e) => (typeof t?._count == "boolean" && e.forEach((r) => {
        r._count = r._count._all;
      }), e);
    }
    __name(nl, "nl");
    function Gi(t, e) {
      return e({ action: "groupBy", unpacker: nl(t), argsMapper: rl })(t);
    }
    __name(Gi, "Gi");
    function Wi(t, e, r) {
      if (e === "aggregate") return (n) => Qi(n, r);
      if (e === "count") return (n) => Ji(n, r);
      if (e === "groupBy") return (n) => Gi(n, r);
    }
    __name(Wi, "Wi");
    u();
    c();
    m();
    p();
    d();
    l();
    function Ki(t, e) {
      let r = e.fields.filter((i) => !i.relationName), n = ii(r, "name");
      return new Proxy({}, { get(i, o) {
        if (o in i || typeof o == "symbol") return i[o];
        let s = n[o];
        if (s) return new yt(t, o, s.type, s.isList, s.kind === "enum");
      }, ...lr(Object.keys(n)) });
    }
    __name(Ki, "Ki");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    var Hi = /* @__PURE__ */ __name((t) => Array.isArray(t) ? t : t.split("."), "Hi");
    var Zr = /* @__PURE__ */ __name((t, e) => Hi(e).reduce((r, n) => r && r[n], t), "Zr");
    var zi = /* @__PURE__ */ __name((t, e, r) => Hi(e).reduceRight((n, i, o, s) => Object.assign({}, Zr(t, s.slice(0, o)), { [i]: n }), r), "zi");
    function il(t, e) {
      return t === void 0 || e === void 0 ? [] : [...e, "select", t];
    }
    __name(il, "il");
    function ol(t, e, r) {
      return e === void 0 ? t ?? {} : zi(e, r, t || true);
    }
    __name(ol, "ol");
    function en(t, e, r, n, i, o) {
      let a = t._runtimeDataModel.models[e].fields.reduce((f, y) => ({ ...f, [y.name]: y }), {});
      return (f) => {
        let y = Re(t._errorFormat), C = il(n, i), R = ol(f, o, C), O = r({ dataPath: C, callsite: y })(R), A = sl(t, e);
        return new Proxy(O, { get(I, k) {
          if (!A.includes(k)) return I[k];
          let se = [a[k].type, r, k], z = [C, R];
          return en(t, ...se, ...z);
        }, ...lr([...A, ...Object.getOwnPropertyNames(O)]) });
      };
    }
    __name(en, "en");
    function sl(t, e) {
      return t._runtimeDataModel.models[e].fields.filter((r) => r.kind === "object").map((r) => r.name);
    }
    __name(sl, "sl");
    var al = ["findUnique", "findUniqueOrThrow", "findFirst", "findFirstOrThrow", "create", "update", "upsert", "delete"];
    var ll = ["aggregate", "count", "groupBy"];
    function tn(t, e) {
      let r = t._extensions.getAllModelExtensions(e) ?? {}, n = [ul(t, e), ml(t, e), Tt(r), H("name", () => e), H("$name", () => e), H("$parent", () => t._appliedParent)];
      return le({}, n);
    }
    __name(tn, "tn");
    function ul(t, e) {
      let r = ge(e), n = Object.keys(pt).concat("count");
      return { getKeys() {
        return n;
      }, getPropertyValue(i) {
        let o = i, s = /* @__PURE__ */ __name((a) => (f) => {
          let y = Re(t._errorFormat);
          return t._createPrismaPromise((C) => {
            let R = { args: f, dataPath: [], action: o, model: e, clientMethod: `${r}.${i}`, jsModelName: r, transaction: C, callsite: y };
            return t._request({ ...R, ...a });
          }, { action: o, args: f, model: e });
        }, "s");
        return al.includes(o) ? en(t, e, s) : cl(i) ? Wi(t, i, s) : s({});
      } };
    }
    __name(ul, "ul");
    function cl(t) {
      return ll.includes(t);
    }
    __name(cl, "cl");
    function ml(t, e) {
      return _e(H("fields", () => {
        let r = t._runtimeDataModel.models[e];
        return Ki(e, r);
      }));
    }
    __name(ml, "ml");
    u();
    c();
    m();
    p();
    d();
    l();
    function Yi(t) {
      return t.replace(/^./, (e) => e.toUpperCase());
    }
    __name(Yi, "Yi");
    var rn = Symbol();
    function Ct(t) {
      let e = [pl(t), dl(t), H(rn, () => t), H("$parent", () => t._appliedParent)], r = t._extensions.getAllClientExtensions();
      return r && e.push(Tt(r)), le(t, e);
    }
    __name(Ct, "Ct");
    function pl(t) {
      let e = Object.getPrototypeOf(t._originalClient), r = [...new Set(Object.getOwnPropertyNames(e))];
      return { getKeys() {
        return r;
      }, getPropertyValue(n) {
        return t[n];
      } };
    }
    __name(pl, "pl");
    function dl(t) {
      let e = Object.keys(t._runtimeDataModel.models), r = e.map(ge), n = [...new Set(e.concat(r))];
      return _e({ getKeys() {
        return n;
      }, getPropertyValue(i) {
        let o = Yi(i);
        if (t._runtimeDataModel.models[o] !== void 0) return tn(t, o);
        if (t._runtimeDataModel.models[i] !== void 0) return tn(t, i);
      }, getPropertyDescriptor(i) {
        if (!r.includes(i)) return { enumerable: false };
      } });
    }
    __name(dl, "dl");
    function Xi(t) {
      return t[rn] ? t[rn] : t;
    }
    __name(Xi, "Xi");
    function Zi(t) {
      if (typeof t == "function") return t(this);
      if (t.client?.__AccelerateEngine) {
        let r = t.client.__AccelerateEngine;
        this._originalClient._engine = new r(this._originalClient._accelerateEngineConfig);
      }
      let e = Object.create(this._originalClient, { _extensions: { value: this._extensions.append(t) }, _appliedParent: { value: this, configurable: true }, $use: { value: void 0 }, $on: { value: void 0 } });
      return Ct(e);
    }
    __name(Zi, "Zi");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    function eo({ result: t, modelName: e, select: r, omit: n, extensions: i }) {
      let o = i.getAllComputedFields(e);
      if (!o) return t;
      let s = [], a = [];
      for (let f of Object.values(o)) {
        if (n) {
          if (n[f.name]) continue;
          let y = f.needs.filter((C) => n[C]);
          y.length > 0 && a.push(et(y));
        } else if (r) {
          if (!r[f.name]) continue;
          let y = f.needs.filter((C) => !r[C]);
          y.length > 0 && a.push(et(y));
        }
        fl(t, f.needs) && s.push(gl(f, le(t, s)));
      }
      return s.length > 0 || a.length > 0 ? le(t, [...s, ...a]) : t;
    }
    __name(eo, "eo");
    function fl(t, e) {
      return e.every((r) => Nr(t, r));
    }
    __name(fl, "fl");
    function gl(t, e) {
      return _e(H(t.name, () => t.compute(e)));
    }
    __name(gl, "gl");
    u();
    c();
    m();
    p();
    d();
    l();
    function pr({ visitor: t, result: e, args: r, runtimeDataModel: n, modelName: i }) {
      if (Array.isArray(e)) {
        for (let s = 0; s < e.length; s++) e[s] = pr({ result: e[s], args: r, modelName: i, runtimeDataModel: n, visitor: t });
        return e;
      }
      let o = t(e, i, r) ?? e;
      return r.include && to({ includeOrSelect: r.include, result: o, parentModelName: i, runtimeDataModel: n, visitor: t }), r.select && to({ includeOrSelect: r.select, result: o, parentModelName: i, runtimeDataModel: n, visitor: t }), o;
    }
    __name(pr, "pr");
    function to({ includeOrSelect: t, result: e, parentModelName: r, runtimeDataModel: n, visitor: i }) {
      for (let [o, s] of Object.entries(t)) {
        if (!s || e[o] == null || ye(s)) continue;
        let f = n.models[r].fields.find((C) => C.name === o);
        if (!f || f.kind !== "object" || !f.relationName) continue;
        let y = typeof s == "object" ? s : {};
        e[o] = pr({ visitor: i, result: e[o], args: y, modelName: f.type, runtimeDataModel: n });
      }
    }
    __name(to, "to");
    function ro({ result: t, modelName: e, args: r, extensions: n, runtimeDataModel: i, globalOmit: o }) {
      return n.isEmpty() || t == null || typeof t != "object" || !i.models[e] ? t : pr({ result: t, args: r ?? {}, modelName: e, runtimeDataModel: i, visitor: /* @__PURE__ */ __name((a, f, y) => {
        let C = ge(f);
        return eo({ result: a, modelName: C, select: y.select, omit: y.select ? void 0 : { ...o?.[C], ...y.omit }, extensions: n });
      }, "visitor") });
    }
    __name(ro, "ro");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    var yl = ["$connect", "$disconnect", "$on", "$transaction", "$use", "$extends"];
    var no = yl;
    function io(t) {
      if (t instanceof ee) return hl(t);
      if (or(t)) return bl(t);
      if (Array.isArray(t)) {
        let r = [t[0]];
        for (let n = 1; n < t.length; n++) r[n] = Rt(t[n]);
        return r;
      }
      let e = {};
      for (let r in t) e[r] = Rt(t[r]);
      return e;
    }
    __name(io, "io");
    function hl(t) {
      return new ee(t.strings, t.values);
    }
    __name(hl, "hl");
    function bl(t) {
      return new vt(t.sql, t.values);
    }
    __name(bl, "bl");
    function Rt(t) {
      if (typeof t != "object" || t == null || t instanceof Pe || ze(t)) return t;
      if (Qe(t)) return new pe(t.toFixed());
      if (je(t)) return /* @__PURE__ */ new Date(+t);
      if (ArrayBuffer.isView(t)) return t.slice(0);
      if (Array.isArray(t)) {
        let e = t.length, r;
        for (r = Array(e); e--; ) r[e] = Rt(t[e]);
        return r;
      }
      if (typeof t == "object") {
        let e = {};
        for (let r in t) r === "__proto__" ? Object.defineProperty(e, r, { value: Rt(t[r]), configurable: true, enumerable: true, writable: true }) : e[r] = Rt(t[r]);
        return e;
      }
      Ee(t, "Unknown value");
    }
    __name(Rt, "Rt");
    function so(t, e, r, n = 0) {
      return t._createPrismaPromise((i) => {
        let o = e.customDataProxyFetch;
        return "transaction" in e && i !== void 0 && (e.transaction?.kind === "batch" && e.transaction.lock.then(), e.transaction = i), n === r.length ? t._executeRequest(e) : r[n]({ model: e.model, operation: e.model ? e.action : e.clientMethod, args: io(e.args ?? {}), __internalParams: e, query: /* @__PURE__ */ __name((s, a = e) => {
          let f = a.customDataProxyFetch;
          return a.customDataProxyFetch = co(o, f), a.args = s, so(t, a, r, n + 1);
        }, "query") });
      });
    }
    __name(so, "so");
    function ao(t, e) {
      let { jsModelName: r, action: n, clientMethod: i } = e, o = r ? n : i;
      if (t._extensions.isEmpty()) return t._executeRequest(e);
      let s = t._extensions.getAllQueryCallbacks(r ?? "$none", o);
      return so(t, e, s);
    }
    __name(ao, "ao");
    function lo(t) {
      return (e) => {
        let r = { requests: e }, n = e[0].extensions.getAllBatchQueryCallbacks();
        return n.length ? uo(r, n, 0, t) : t(r);
      };
    }
    __name(lo, "lo");
    function uo(t, e, r, n) {
      if (r === e.length) return n(t);
      let i = t.customDataProxyFetch, o = t.requests[0].transaction;
      return e[r]({ args: { queries: t.requests.map((s) => ({ model: s.modelName, operation: s.action, args: s.args })), transaction: o ? { isolationLevel: o.kind === "batch" ? o.isolationLevel : void 0 } : void 0 }, __internalParams: t, query(s, a = t) {
        let f = a.customDataProxyFetch;
        return a.customDataProxyFetch = co(i, f), uo(a, e, r + 1, n);
      } });
    }
    __name(uo, "uo");
    var oo = /* @__PURE__ */ __name((t) => t, "oo");
    function co(t = oo, e = oo) {
      return (r) => t(e(r));
    }
    __name(co, "co");
    u();
    c();
    m();
    p();
    d();
    l();
    var mo = G("prisma:client");
    var po = { Vercel: "vercel", "Netlify CI": "netlify" };
    function fo({ postinstall: t, ciName: e, clientVersion: r }) {
      if (mo("checkPlatformCaching:postinstall", t), mo("checkPlatformCaching:ciName", e), t === true && e && e in po) {
        let n = `Prisma has detected that this project was built on ${e}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${po[e]}-build`;
        throw console.error(n), new D(n, r);
      }
    }
    __name(fo, "fo");
    u();
    c();
    m();
    p();
    d();
    l();
    function go(t, e) {
      return t ? t.datasources ? t.datasources : t.datasourceUrl ? { [e[0]]: { url: t.datasourceUrl } } : {} : {};
    }
    __name(go, "go");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    var wl = /* @__PURE__ */ __name(() => globalThis.process?.release?.name === "node", "wl");
    var El = /* @__PURE__ */ __name(() => !!globalThis.Bun || !!globalThis.process?.versions?.bun, "El");
    var xl = /* @__PURE__ */ __name(() => !!globalThis.Deno, "xl");
    var Pl = /* @__PURE__ */ __name(() => typeof globalThis.Netlify == "object", "Pl");
    var vl = /* @__PURE__ */ __name(() => typeof globalThis.EdgeRuntime == "object", "vl");
    var Tl = /* @__PURE__ */ __name(() => globalThis.navigator?.userAgent === "Cloudflare-Workers", "Tl");
    function Cl() {
      return [[Pl, "netlify"], [vl, "edge-light"], [Tl, "workerd"], [xl, "deno"], [El, "bun"], [wl, "node"]].flatMap((r) => r[0]() ? [r[1]] : []).at(0) ?? "";
    }
    __name(Cl, "Cl");
    var Rl = { node: "Node.js", workerd: "Cloudflare Workers", deno: "Deno and Deno Deploy", netlify: "Netlify Edge Functions", "edge-light": "Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)" };
    function Ae() {
      let t = Cl();
      return { id: t, prettyName: Rl[t] || t, isEdge: ["workerd", "deno", "netlify", "edge-light"].includes(t) };
    }
    __name(Ae, "Ae");
    u();
    c();
    m();
    p();
    d();
    l();
    var yo = "6.13.0";
    u();
    c();
    m();
    p();
    d();
    l();
    function dr({ inlineDatasources: t, overrideDatasources: e, env: r, clientVersion: n }) {
      let i, o = Object.keys(t)[0], s = t[o]?.url, a = e[o]?.url;
      if (o === void 0 ? i = void 0 : a ? i = a : s?.value ? i = s.value : s?.fromEnvVar && (i = r[s.fromEnvVar]), s?.fromEnvVar !== void 0 && i === void 0) throw Ae().id === "workerd" ? new D(`error: Environment variable not found: ${s.fromEnvVar}.

In Cloudflare module Workers, environment variables are available only in the Worker's \`env\` parameter of \`fetch\`.
To solve this, provide the connection string directly: https://pris.ly/d/cloudflare-datasource-url`, n) : new D(`error: Environment variable not found: ${s.fromEnvVar}.`, n);
      if (i === void 0) throw new D("error: Missing URL environment variable, value, or override.", n);
      return i;
    }
    __name(dr, "dr");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    function ho(t) {
      if (t?.kind === "itx") return t.options.id;
    }
    __name(ho, "ho");
    u();
    c();
    m();
    p();
    d();
    l();
    var nn;
    var bo = { async loadLibrary(t) {
      let { clientVersion: e, adapter: r, engineWasm: n } = t;
      if (r === void 0) throw new D(`The \`adapter\` option for \`PrismaClient\` is required in this context (${Ae().prettyName})`, e);
      if (n === void 0) throw new D("WASM engine was unexpectedly `undefined`", e);
      nn === void 0 && (nn = (async () => {
        let o = await n.getRuntime(), s = await n.getQueryEngineWasmModule();
        if (s == null) throw new D("The loaded wasm module was unexpectedly `undefined` or `null` once loaded", e);
        let a = { "./query_engine_bg.js": o }, f = new WebAssembly.Instance(s, a), y = f.exports.__wbindgen_start;
        return o.__wbg_set_wasm(f.exports), y(), o.QueryEngine;
      })());
      let i = await nn;
      return { debugPanic() {
        return Promise.reject("{}");
      }, dmmf() {
        return Promise.resolve("{}");
      }, version() {
        return { commit: "unknown", version: "unknown" };
      }, QueryEngine: i };
    } };
    var Sl = "P2036";
    var he = G("prisma:client:libraryEngine");
    function kl(t) {
      return t.item_type === "query" && "query" in t;
    }
    __name(kl, "kl");
    function Ol(t) {
      return "level" in t ? t.level === "error" && t.message === "PANIC" : false;
    }
    __name(Ol, "Ol");
    var BS = [...Mr, "native"];
    var Il = 0xffffffffffffffffn;
    var on = 1n;
    function Ml() {
      let t = on++;
      return on > Il && (on = 1n), t;
    }
    __name(Ml, "Ml");
    var At = class {
      static {
        __name(this, "At");
      }
      name = "LibraryEngine";
      engine;
      libraryInstantiationPromise;
      libraryStartingPromise;
      libraryStoppingPromise;
      libraryStarted;
      executingQueryPromise;
      config;
      QueryEngineConstructor;
      libraryLoader;
      library;
      logEmitter;
      libQueryEnginePath;
      binaryTarget;
      datasourceOverrides;
      datamodel;
      logQueries;
      logLevel;
      lastQuery;
      loggerRustPanic;
      tracingHelper;
      adapterPromise;
      versionInfo;
      constructor(e, r) {
        this.libraryLoader = r ?? bo, this.config = e, this.libraryStarted = false, this.logQueries = e.logQueries ?? false, this.logLevel = e.logLevel ?? "error", this.logEmitter = e.logEmitter, this.datamodel = e.inlineSchema, this.tracingHelper = e.tracingHelper, e.enableDebugLogs && (this.logLevel = "debug");
        let n = Object.keys(e.overrideDatasources)[0], i = e.overrideDatasources[n]?.url;
        n !== void 0 && i !== void 0 && (this.datasourceOverrides = { [n]: i }), this.libraryInstantiationPromise = this.instantiateLibrary();
      }
      wrapEngine(e) {
        return { applyPendingMigrations: e.applyPendingMigrations?.bind(e), commitTransaction: this.withRequestId(e.commitTransaction.bind(e)), connect: this.withRequestId(e.connect.bind(e)), disconnect: this.withRequestId(e.disconnect.bind(e)), metrics: e.metrics?.bind(e), query: this.withRequestId(e.query.bind(e)), rollbackTransaction: this.withRequestId(e.rollbackTransaction.bind(e)), sdlSchema: e.sdlSchema?.bind(e), startTransaction: this.withRequestId(e.startTransaction.bind(e)), trace: e.trace.bind(e), free: e.free?.bind(e) };
      }
      withRequestId(e) {
        return async (...r) => {
          let n = Ml().toString();
          try {
            return await e(...r, n);
          } finally {
            if (this.tracingHelper.isEnabled()) {
              let i = await this.engine?.trace(n);
              if (i) {
                let o = JSON.parse(i);
                this.tracingHelper.dispatchEngineSpans(o.spans);
              }
            }
          }
        };
      }
      async applyPendingMigrations() {
        throw new Error("Cannot call this method from this type of engine instance");
      }
      async transaction(e, r, n) {
        await this.start();
        let i = await this.adapterPromise, o = JSON.stringify(r), s;
        if (e === "start") {
          let f = JSON.stringify({ max_wait: n.maxWait, timeout: n.timeout, isolation_level: n.isolationLevel });
          s = await this.engine?.startTransaction(f, o);
        } else e === "commit" ? s = await this.engine?.commitTransaction(n.id, o) : e === "rollback" && (s = await this.engine?.rollbackTransaction(n.id, o));
        let a = this.parseEngineResponse(s);
        if (Dl(a)) {
          let f = this.getExternalAdapterError(a, i?.errorRegistry);
          throw f ? f.error : new Z(a.message, { code: a.error_code, clientVersion: this.config.clientVersion, meta: a.meta });
        } else if (typeof a.message == "string") throw new Q(a.message, { clientVersion: this.config.clientVersion });
        return a;
      }
      async instantiateLibrary() {
        if (he("internalSetup"), this.libraryInstantiationPromise) return this.libraryInstantiationPromise;
        this.binaryTarget = await this.getCurrentBinaryTarget(), await this.tracingHelper.runInChildSpan("load_engine", () => this.loadEngine()), this.version();
      }
      async getCurrentBinaryTarget() {
      }
      parseEngineResponse(e) {
        if (!e) throw new Q("Response from the Engine was empty", { clientVersion: this.config.clientVersion });
        try {
          return JSON.parse(e);
        } catch {
          throw new Q("Unable to JSON.parse response from engine", { clientVersion: this.config.clientVersion });
        }
      }
      async loadEngine() {
        if (!this.engine) {
          this.QueryEngineConstructor || (this.library = await this.libraryLoader.loadLibrary(this.config), this.QueryEngineConstructor = this.library.QueryEngine);
          try {
            let e = new w(this);
            this.adapterPromise || (this.adapterPromise = this.config.adapter?.connect()?.then(Bt));
            let r = await this.adapterPromise;
            r && he("Using driver adapter: %O", r), this.engine = this.wrapEngine(new this.QueryEngineConstructor({ datamodel: this.datamodel, env: g.env, logQueries: this.config.logQueries ?? false, ignoreEnvVarErrors: true, datasourceOverrides: this.datasourceOverrides ?? {}, logLevel: this.logLevel, configDir: this.config.cwd, engineProtocol: "json", enableTracing: this.tracingHelper.isEnabled() }, (n) => {
              e.deref()?.logger(n);
            }, r));
          } catch (e) {
            let r = e, n = this.parseInitError(r.message);
            throw typeof n == "string" ? r : new D(n.message, this.config.clientVersion, n.error_code);
          }
        }
      }
      logger(e) {
        let r = this.parseEngineResponse(e);
        r && (r.level = r?.level.toLowerCase() ?? "unknown", kl(r) ? this.logEmitter.emit("query", { timestamp: /* @__PURE__ */ new Date(), query: r.query, params: r.params, duration: Number(r.duration_ms), target: r.module_path }) : (Ol(r), this.logEmitter.emit(r.level, { timestamp: /* @__PURE__ */ new Date(), message: r.message, target: r.module_path })));
      }
      parseInitError(e) {
        try {
          return JSON.parse(e);
        } catch {
        }
        return e;
      }
      parseRequestError(e) {
        try {
          return JSON.parse(e);
        } catch {
        }
        return e;
      }
      onBeforeExit() {
        throw new Error('"beforeExit" hook is not applicable to the library engine since Prisma 5.0.0, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.');
      }
      async start() {
        if (this.libraryInstantiationPromise || (this.libraryInstantiationPromise = this.instantiateLibrary()), await this.libraryInstantiationPromise, await this.libraryStoppingPromise, this.libraryStartingPromise) return he(`library already starting, this.libraryStarted: ${this.libraryStarted}`), this.libraryStartingPromise;
        if (this.libraryStarted) return;
        let e = /* @__PURE__ */ __name(async () => {
          he("library starting");
          try {
            let r = { traceparent: this.tracingHelper.getTraceParent() };
            await this.engine?.connect(JSON.stringify(r)), this.libraryStarted = true, this.adapterPromise || (this.adapterPromise = this.config.adapter?.connect()?.then(Bt)), await this.adapterPromise, he("library started");
          } catch (r) {
            let n = this.parseInitError(r.message);
            throw typeof n == "string" ? r : new D(n.message, this.config.clientVersion, n.error_code);
          } finally {
            this.libraryStartingPromise = void 0;
          }
        }, "e");
        return this.libraryStartingPromise = this.tracingHelper.runInChildSpan("connect", e), this.libraryStartingPromise;
      }
      async stop() {
        if (await this.libraryInstantiationPromise, await this.libraryStartingPromise, await this.executingQueryPromise, this.libraryStoppingPromise) return he("library is already stopping"), this.libraryStoppingPromise;
        if (!this.libraryStarted) {
          await (await this.adapterPromise)?.dispose(), this.adapterPromise = void 0;
          return;
        }
        let e = /* @__PURE__ */ __name(async () => {
          await new Promise((n) => setImmediate(n)), he("library stopping");
          let r = { traceparent: this.tracingHelper.getTraceParent() };
          await this.engine?.disconnect(JSON.stringify(r)), this.engine?.free && this.engine.free(), this.engine = void 0, this.libraryStarted = false, this.libraryStoppingPromise = void 0, this.libraryInstantiationPromise = void 0, await (await this.adapterPromise)?.dispose(), this.adapterPromise = void 0, he("library stopped");
        }, "e");
        return this.libraryStoppingPromise = this.tracingHelper.runInChildSpan("disconnect", e), this.libraryStoppingPromise;
      }
      version() {
        return this.versionInfo = this.library?.version(), this.versionInfo?.version ?? "unknown";
      }
      debugPanic(e) {
        return this.library?.debugPanic(e);
      }
      async request(e, { traceparent: r, interactiveTransaction: n }) {
        he(`sending request, this.libraryStarted: ${this.libraryStarted}`);
        let i = JSON.stringify({ traceparent: r }), o = JSON.stringify(e);
        try {
          await this.start();
          let s = await this.adapterPromise;
          this.executingQueryPromise = this.engine?.query(o, i, n?.id), this.lastQuery = o;
          let a = this.parseEngineResponse(await this.executingQueryPromise);
          if (a.errors) throw a.errors.length === 1 ? this.buildQueryError(a.errors[0], s?.errorRegistry) : new Q(JSON.stringify(a.errors), { clientVersion: this.config.clientVersion });
          if (this.loggerRustPanic) throw this.loggerRustPanic;
          return { data: a };
        } catch (s) {
          if (s instanceof D) throw s;
          s.code === "GenericFailure" && s.message?.startsWith("PANIC:");
          let a = this.parseRequestError(s.message);
          throw typeof a == "string" ? s : new Q(`${a.message}
${a.backtrace}`, { clientVersion: this.config.clientVersion });
        }
      }
      async requestBatch(e, { transaction: r, traceparent: n }) {
        he("requestBatch");
        let i = ur(e, r);
        await this.start();
        let o = await this.adapterPromise;
        this.lastQuery = JSON.stringify(i), this.executingQueryPromise = this.engine?.query(this.lastQuery, JSON.stringify({ traceparent: n }), ho(r));
        let s = await this.executingQueryPromise, a = this.parseEngineResponse(s);
        if (a.errors) throw a.errors.length === 1 ? this.buildQueryError(a.errors[0], o?.errorRegistry) : new Q(JSON.stringify(a.errors), { clientVersion: this.config.clientVersion });
        let { batchResult: f, errors: y } = a;
        if (Array.isArray(f)) return f.map((C) => C.errors && C.errors.length > 0 ? this.loggerRustPanic ?? this.buildQueryError(C.errors[0], o?.errorRegistry) : { data: C });
        throw y && y.length === 1 ? new Error(y[0].error) : new Error(JSON.stringify(a));
      }
      buildQueryError(e, r) {
        e.user_facing_error.is_panic;
        let n = this.getExternalAdapterError(e.user_facing_error, r);
        return n ? n.error : cr(e, this.config.clientVersion, this.config.activeProvider);
      }
      getExternalAdapterError(e, r) {
        if (e.error_code === Sl && r) {
          let n = e.meta?.id;
          $t(typeof n == "number", "Malformed external JS error received from the engine");
          let i = r.consumeError(n);
          return $t(i, "External error with reported id was not registered"), i;
        }
      }
      async metrics(e) {
        await this.start();
        let r = await this.engine.metrics(JSON.stringify(e));
        return e.format === "prometheus" ? r : this.parseEngineResponse(r);
      }
    };
    function Dl(t) {
      return typeof t == "object" && t !== null && t.error_code !== void 0;
    }
    __name(Dl, "Dl");
    u();
    c();
    m();
    p();
    d();
    l();
    var St = "Accelerate has not been setup correctly. Make sure your client is using `.$extends(withAccelerate())`. See https://pris.ly/d/accelerate-getting-started";
    var fr = class {
      static {
        __name(this, "fr");
      }
      constructor(e) {
        this.config = e;
        this.resolveDatasourceUrl = this.config.accelerateUtils?.resolveDatasourceUrl, this.getBatchRequestPayload = this.config.accelerateUtils?.getBatchRequestPayload, this.prismaGraphQLToJSError = this.config.accelerateUtils?.prismaGraphQLToJSError, this.PrismaClientUnknownRequestError = this.config.accelerateUtils?.PrismaClientUnknownRequestError, this.PrismaClientInitializationError = this.config.accelerateUtils?.PrismaClientInitializationError, this.PrismaClientKnownRequestError = this.config.accelerateUtils?.PrismaClientKnownRequestError, this.debug = this.config.accelerateUtils?.debug, this.engineVersion = this.config.accelerateUtils?.engineVersion, this.clientVersion = this.config.accelerateUtils?.clientVersion;
      }
      name = "AccelerateEngine";
      resolveDatasourceUrl;
      getBatchRequestPayload;
      prismaGraphQLToJSError;
      PrismaClientUnknownRequestError;
      PrismaClientInitializationError;
      PrismaClientKnownRequestError;
      debug;
      engineVersion;
      clientVersion;
      onBeforeExit(e) {
      }
      async start() {
      }
      async stop() {
      }
      version(e) {
        return "unknown";
      }
      transaction(e, r, n) {
        throw new D(St, this.config.clientVersion);
      }
      metrics(e) {
        throw new D(St, this.config.clientVersion);
      }
      request(e, r) {
        throw new D(St, this.config.clientVersion);
      }
      requestBatch(e, r) {
        throw new D(St, this.config.clientVersion);
      }
      applyPendingMigrations() {
        throw new D(St, this.config.clientVersion);
      }
    };
    u();
    c();
    m();
    p();
    d();
    l();
    function wo({ url: t, adapter: e, copyEngine: r, targetBuildType: n }) {
      let i = [], o = [], s = /* @__PURE__ */ __name((k) => {
        i.push({ _tag: "warning", value: k });
      }, "s"), a = /* @__PURE__ */ __name((k) => {
        let M = k.join(`
`);
        o.push({ _tag: "error", value: M });
      }, "a"), f = !!t?.startsWith("prisma://"), y = Fr(t), C = !!e, R = f || y;
      !C && r && R && s(["recommend--no-engine", "In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)"]);
      let O = R || !r;
      C && (O || n === "edge") && (n === "edge" ? a(["Prisma Client was configured to use the `adapter` option but it was imported via its `/edge` endpoint.", "Please either remove the `/edge` endpoint or remove the `adapter` from the Prisma Client constructor."]) : r ? f && a(["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.", "Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]) : a(["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.", "Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."]));
      let A = { accelerate: O, ppg: y, driverAdapters: C };
      function I(k) {
        return k.length > 0;
      }
      __name(I, "I");
      return I(o) ? { ok: false, diagnostics: { warnings: i, errors: o }, isUsing: A } : { ok: true, diagnostics: { warnings: i }, isUsing: A };
    }
    __name(wo, "wo");
    function Eo({ copyEngine: t = true }, e) {
      let r;
      try {
        r = dr({ inlineDatasources: e.inlineDatasources, overrideDatasources: e.overrideDatasources, env: { ...e.env, ...g.env }, clientVersion: e.clientVersion });
      } catch {
      }
      let { ok: n, isUsing: i, diagnostics: o } = wo({ url: r, adapter: e.adapter, copyEngine: t, targetBuildType: "wasm-engine-edge" });
      for (let R of o.warnings) ct(...R.value);
      if (!n) {
        let R = o.errors[0];
        throw new K(R.value, { clientVersion: e.clientVersion });
      }
      let s = Be(e.generator), a = s === "library", f = s === "binary", y = s === "client", C = (i.accelerate || i.ppg) && !i.driverAdapters;
      if (i.accelerate, i.driverAdapters) return new At(e);
      if (i.accelerate) return new fr(e);
      {
        let R = [`PrismaClient failed to initialize because it wasn't configured to run in this environment (${Ae().prettyName}).`, "In order to run Prisma Client in an edge runtime, you will need to configure one of the following options:", "- Enable Driver Adapters: https://pris.ly/d/driver-adapters", "- Enable Accelerate: https://pris.ly/d/accelerate"];
        throw new K(R.join(`
`), { clientVersion: e.clientVersion });
      }
      return "wasm-engine-edge";
    }
    __name(Eo, "Eo");
    u();
    c();
    m();
    p();
    d();
    l();
    function gr({ generator: t }) {
      return t?.previewFeatures ?? [];
    }
    __name(gr, "gr");
    u();
    c();
    m();
    p();
    d();
    l();
    var xo = /* @__PURE__ */ __name((t) => ({ command: t }), "xo");
    u();
    c();
    m();
    p();
    d();
    l();
    u();
    c();
    m();
    p();
    d();
    l();
    var Po = /* @__PURE__ */ __name((t) => t.strings.reduce((e, r, n) => `${e}@P${n}${r}`), "Po");
    u();
    c();
    m();
    p();
    d();
    l();
    l();
    function rt(t) {
      try {
        return vo(t, "fast");
      } catch {
        return vo(t, "slow");
      }
    }
    __name(rt, "rt");
    function vo(t, e) {
      return JSON.stringify(t.map((r) => Co(r, e)));
    }
    __name(vo, "vo");
    function Co(t, e) {
      if (Array.isArray(t)) return t.map((r) => Co(r, e));
      if (typeof t == "bigint") return { prisma__type: "bigint", prisma__value: t.toString() };
      if (je(t)) return { prisma__type: "date", prisma__value: t.toJSON() };
      if (pe.isDecimal(t)) return { prisma__type: "decimal", prisma__value: t.toJSON() };
      if (b.isBuffer(t)) return { prisma__type: "bytes", prisma__value: t.toString("base64") };
      if (_l(t)) return { prisma__type: "bytes", prisma__value: b.from(t).toString("base64") };
      if (ArrayBuffer.isView(t)) {
        let { buffer: r, byteOffset: n, byteLength: i } = t;
        return { prisma__type: "bytes", prisma__value: b.from(r, n, i).toString("base64") };
      }
      return typeof t == "object" && e === "slow" ? Ro(t) : t;
    }
    __name(Co, "Co");
    function _l(t) {
      return t instanceof ArrayBuffer || t instanceof SharedArrayBuffer ? true : typeof t == "object" && t !== null ? t[Symbol.toStringTag] === "ArrayBuffer" || t[Symbol.toStringTag] === "SharedArrayBuffer" : false;
    }
    __name(_l, "_l");
    function Ro(t) {
      if (typeof t != "object" || t === null) return t;
      if (typeof t.toJSON == "function") return t.toJSON();
      if (Array.isArray(t)) return t.map(To);
      let e = {};
      for (let r of Object.keys(t)) e[r] = To(t[r]);
      return e;
    }
    __name(Ro, "Ro");
    function To(t) {
      return typeof t == "bigint" ? t.toString() : Ro(t);
    }
    __name(To, "To");
    var Ll = /^(\s*alter\s)/i;
    var Ao = G("prisma:client");
    function sn(t, e, r, n) {
      if (!(t !== "postgresql" && t !== "cockroachdb") && r.length > 0 && Ll.exec(e)) throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`);
    }
    __name(sn, "sn");
    var an = /* @__PURE__ */ __name(({ clientMethod: t, activeProvider: e }) => (r) => {
      let n = "", i;
      if (or(r)) n = r.sql, i = { values: rt(r.values), __prismaRawParameters__: true };
      else if (Array.isArray(r)) {
        let [o, ...s] = r;
        n = o, i = { values: rt(s || []), __prismaRawParameters__: true };
      } else switch (e) {
        case "sqlite":
        case "mysql": {
          n = r.sql, i = { values: rt(r.values), __prismaRawParameters__: true };
          break;
        }
        case "cockroachdb":
        case "postgresql":
        case "postgres": {
          n = r.text, i = { values: rt(r.values), __prismaRawParameters__: true };
          break;
        }
        case "sqlserver": {
          n = Po(r), i = { values: rt(r.values), __prismaRawParameters__: true };
          break;
        }
        default:
          throw new Error(`The ${e} provider does not support ${t}`);
      }
      return i?.values ? Ao(`prisma.${t}(${n}, ${i.values})`) : Ao(`prisma.${t}(${n})`), { query: n, parameters: i };
    }, "an");
    var So = { requestArgsToMiddlewareArgs(t) {
      return [t.strings, ...t.values];
    }, middlewareArgsToRequestArgs(t) {
      let [e, ...r] = t;
      return new ee(e, r);
    } };
    var ko = { requestArgsToMiddlewareArgs(t) {
      return [t];
    }, middlewareArgsToRequestArgs(t) {
      return t[0];
    } };
    u();
    c();
    m();
    p();
    d();
    l();
    function ln(t) {
      return function(r, n) {
        let i, o = /* @__PURE__ */ __name((s = t) => {
          try {
            return s === void 0 || s?.kind === "itx" ? i ??= Oo(r(s)) : Oo(r(s));
          } catch (a) {
            return Promise.reject(a);
          }
        }, "o");
        return { get spec() {
          return n;
        }, then(s, a) {
          return o().then(s, a);
        }, catch(s) {
          return o().catch(s);
        }, finally(s) {
          return o().finally(s);
        }, requestTransaction(s) {
          let a = o(s);
          return a.requestTransaction ? a.requestTransaction(s) : a;
        }, [Symbol.toStringTag]: "PrismaPromise" };
      };
    }
    __name(ln, "ln");
    function Oo(t) {
      return typeof t.then == "function" ? t : Promise.resolve(t);
    }
    __name(Oo, "Oo");
    u();
    c();
    m();
    p();
    d();
    l();
    var Fl = Dr.split(".")[0];
    var Ul = { isEnabled() {
      return false;
    }, getTraceParent() {
      return "00-10-10-00";
    }, dispatchEngineSpans() {
    }, getActiveContext() {
    }, runInChildSpan(t, e) {
      return e();
    } };
    var un = class {
      static {
        __name(this, "un");
      }
      isEnabled() {
        return this.getGlobalTracingHelper().isEnabled();
      }
      getTraceParent(e) {
        return this.getGlobalTracingHelper().getTraceParent(e);
      }
      dispatchEngineSpans(e) {
        return this.getGlobalTracingHelper().dispatchEngineSpans(e);
      }
      getActiveContext() {
        return this.getGlobalTracingHelper().getActiveContext();
      }
      runInChildSpan(e, r) {
        return this.getGlobalTracingHelper().runInChildSpan(e, r);
      }
      getGlobalTracingHelper() {
        let e = globalThis[`V${Fl}_PRISMA_INSTRUMENTATION`], r = globalThis.PRISMA_INSTRUMENTATION;
        return e?.helper ?? r?.helper ?? Ul;
      }
    };
    function Io() {
      return new un();
    }
    __name(Io, "Io");
    u();
    c();
    m();
    p();
    d();
    l();
    function Mo(t, e = () => {
    }) {
      let r, n = new Promise((i) => r = i);
      return { then(i) {
        return --t === 0 && r(e()), i?.(n);
      } };
    }
    __name(Mo, "Mo");
    u();
    c();
    m();
    p();
    d();
    l();
    function Do(t) {
      return typeof t == "string" ? t : t.reduce((e, r) => {
        let n = typeof r == "string" ? r : r.level;
        return n === "query" ? e : e && (r === "info" || e === "info") ? "info" : n;
      }, void 0);
    }
    __name(Do, "Do");
    u();
    c();
    m();
    p();
    d();
    l();
    var yr = class {
      static {
        __name(this, "yr");
      }
      _middlewares = [];
      use(e) {
        this._middlewares.push(e);
      }
      get(e) {
        return this._middlewares[e];
      }
      has(e) {
        return !!this._middlewares[e];
      }
      length() {
        return this._middlewares.length;
      }
    };
    u();
    c();
    m();
    p();
    d();
    l();
    var Lo = ot(ti());
    u();
    c();
    m();
    p();
    d();
    l();
    function hr(t) {
      return typeof t.batchRequestIdx == "number";
    }
    __name(hr, "hr");
    u();
    c();
    m();
    p();
    d();
    l();
    function _o(t) {
      if (t.action !== "findUnique" && t.action !== "findUniqueOrThrow") return;
      let e = [];
      return t.modelName && e.push(t.modelName), t.query.arguments && e.push(cn(t.query.arguments)), e.push(cn(t.query.selection)), e.join("");
    }
    __name(_o, "_o");
    function cn(t) {
      return `(${Object.keys(t).sort().map((r) => {
        let n = t[r];
        return typeof n == "object" && n !== null ? `(${r} ${cn(n)})` : r;
      }).join(" ")})`;
    }
    __name(cn, "cn");
    u();
    c();
    m();
    p();
    d();
    l();
    var Nl = { aggregate: false, aggregateRaw: false, createMany: true, createManyAndReturn: true, createOne: true, deleteMany: true, deleteOne: true, executeRaw: true, findFirst: false, findFirstOrThrow: false, findMany: false, findRaw: false, findUnique: false, findUniqueOrThrow: false, groupBy: false, queryRaw: false, runCommandRaw: true, updateMany: true, updateManyAndReturn: true, updateOne: true, upsertOne: true };
    function mn(t) {
      return Nl[t];
    }
    __name(mn, "mn");
    u();
    c();
    m();
    p();
    d();
    l();
    var br = class {
      static {
        __name(this, "br");
      }
      constructor(e) {
        this.options = e;
        this.batches = {};
      }
      batches;
      tickActive = false;
      request(e) {
        let r = this.options.batchBy(e);
        return r ? (this.batches[r] || (this.batches[r] = [], this.tickActive || (this.tickActive = true, g.nextTick(() => {
          this.dispatchBatches(), this.tickActive = false;
        }))), new Promise((n, i) => {
          this.batches[r].push({ request: e, resolve: n, reject: i });
        })) : this.options.singleLoader(e);
      }
      dispatchBatches() {
        for (let e in this.batches) {
          let r = this.batches[e];
          delete this.batches[e], r.length === 1 ? this.options.singleLoader(r[0].request).then((n) => {
            n instanceof Error ? r[0].reject(n) : r[0].resolve(n);
          }).catch((n) => {
            r[0].reject(n);
          }) : (r.sort((n, i) => this.options.batchOrder(n.request, i.request)), this.options.batchLoader(r.map((n) => n.request)).then((n) => {
            if (n instanceof Error) for (let i = 0; i < r.length; i++) r[i].reject(n);
            else for (let i = 0; i < r.length; i++) {
              let o = n[i];
              o instanceof Error ? r[i].reject(o) : r[i].resolve(o);
            }
          }).catch((n) => {
            for (let i = 0; i < r.length; i++) r[i].reject(n);
          }));
        }
      }
      get [Symbol.toStringTag]() {
        return "DataLoader";
      }
    };
    u();
    c();
    m();
    p();
    d();
    l();
    l();
    function Le(t, e) {
      if (e === null) return e;
      switch (t) {
        case "bigint":
          return BigInt(e);
        case "bytes": {
          let { buffer: r, byteOffset: n, byteLength: i } = b.from(e, "base64");
          return new Uint8Array(r, n, i);
        }
        case "decimal":
          return new pe(e);
        case "datetime":
        case "date":
          return new Date(e);
        case "time":
          return /* @__PURE__ */ new Date(`1970-01-01T${e}Z`);
        case "bigint-array":
          return e.map((r) => Le("bigint", r));
        case "bytes-array":
          return e.map((r) => Le("bytes", r));
        case "decimal-array":
          return e.map((r) => Le("decimal", r));
        case "datetime-array":
          return e.map((r) => Le("datetime", r));
        case "date-array":
          return e.map((r) => Le("date", r));
        case "time-array":
          return e.map((r) => Le("time", r));
        default:
          return e;
      }
    }
    __name(Le, "Le");
    function wr(t) {
      let e = [], r = ql(t);
      for (let n = 0; n < t.rows.length; n++) {
        let i = t.rows[n], o = { ...r };
        for (let s = 0; s < i.length; s++) o[t.columns[s]] = Le(t.types[s], i[s]);
        e.push(o);
      }
      return e;
    }
    __name(wr, "wr");
    function ql(t) {
      let e = {};
      for (let r = 0; r < t.columns.length; r++) e[t.columns[r]] = null;
      return e;
    }
    __name(ql, "ql");
    var Bl = G("prisma:client:request_handler");
    var Er = class {
      static {
        __name(this, "Er");
      }
      client;
      dataloader;
      logEmitter;
      constructor(e, r) {
        this.logEmitter = r, this.client = e, this.dataloader = new br({ batchLoader: lo(async ({ requests: n, customDataProxyFetch: i }) => {
          let { transaction: o, otelParentCtx: s } = n[0], a = n.map((R) => R.protocolQuery), f = this.client._tracingHelper.getTraceParent(s), y = n.some((R) => mn(R.protocolQuery.action));
          return (await this.client._engine.requestBatch(a, { traceparent: f, transaction: $l(o), containsWrite: y, customDataProxyFetch: i })).map((R, O) => {
            if (R instanceof Error) return R;
            try {
              return this.mapQueryEngineResult(n[O], R);
            } catch (A) {
              return A;
            }
          });
        }), singleLoader: /* @__PURE__ */ __name(async (n) => {
          let i = n.transaction?.kind === "itx" ? Fo(n.transaction) : void 0, o = await this.client._engine.request(n.protocolQuery, { traceparent: this.client._tracingHelper.getTraceParent(), interactiveTransaction: i, isWrite: mn(n.protocolQuery.action), customDataProxyFetch: n.customDataProxyFetch });
          return this.mapQueryEngineResult(n, o);
        }, "singleLoader"), batchBy: /* @__PURE__ */ __name((n) => n.transaction?.id ? `transaction-${n.transaction.id}` : _o(n.protocolQuery), "batchBy"), batchOrder(n, i) {
          return n.transaction?.kind === "batch" && i.transaction?.kind === "batch" ? n.transaction.index - i.transaction.index : 0;
        } });
      }
      async request(e) {
        try {
          return await this.dataloader.request(e);
        } catch (r) {
          let { clientMethod: n, callsite: i, transaction: o, args: s, modelName: a } = e;
          this.handleAndLogRequestError({ error: r, clientMethod: n, callsite: i, transaction: o, args: s, modelName: a, globalOmit: e.globalOmit });
        }
      }
      mapQueryEngineResult({ dataPath: e, unpacker: r }, n) {
        let i = n?.data, o = this.unpack(i, e, r);
        return g.env.PRISMA_CLIENT_GET_TIME ? { data: o } : o;
      }
      handleAndLogRequestError(e) {
        try {
          this.handleRequestError(e);
        } catch (r) {
          throw this.logEmitter && this.logEmitter.emit("error", { message: r.message, target: e.clientMethod, timestamp: /* @__PURE__ */ new Date() }), r;
        }
      }
      handleRequestError({ error: e, clientMethod: r, callsite: n, transaction: i, args: o, modelName: s, globalOmit: a }) {
        if (Bl(e), Vl(e, i)) throw e;
        if (e instanceof Z && jl(e)) {
          let y = Uo(e.meta);
          Zt({ args: o, errors: [y], callsite: n, errorFormat: this.client._errorFormat, originalMethod: r, clientVersion: this.client._clientVersion, globalOmit: a });
        }
        let f = e.message;
        if (n && (f = jt({ callsite: n, originalMethod: r, isPanic: e.isPanic, showColors: this.client._errorFormat === "pretty", message: f })), f = this.sanitizeMessage(f), e.code) {
          let y = s ? { modelName: s, ...e.meta } : e.meta;
          throw new Z(f, { code: e.code, clientVersion: this.client._clientVersion, meta: y, batchRequestIdx: e.batchRequestIdx });
        } else {
          if (e.isPanic) throw new xe(f, this.client._clientVersion);
          if (e instanceof Q) throw new Q(f, { clientVersion: this.client._clientVersion, batchRequestIdx: e.batchRequestIdx });
          if (e instanceof D) throw new D(f, this.client._clientVersion);
          if (e instanceof xe) throw new xe(f, this.client._clientVersion);
        }
        throw e.clientVersion = this.client._clientVersion, e;
      }
      sanitizeMessage(e) {
        return this.client._errorFormat && this.client._errorFormat !== "pretty" ? (0, Lo.default)(e) : e;
      }
      unpack(e, r, n) {
        if (!e || (e.data && (e = e.data), !e)) return e;
        let i = Object.keys(e)[0], o = Object.values(e)[0], s = r.filter((y) => y !== "select" && y !== "include"), a = Zr(o, s), f = i === "queryRaw" ? wr(a) : Ve(a);
        return n ? n(f) : f;
      }
      get [Symbol.toStringTag]() {
        return "RequestHandler";
      }
    };
    function $l(t) {
      if (t) {
        if (t.kind === "batch") return { kind: "batch", options: { isolationLevel: t.isolationLevel } };
        if (t.kind === "itx") return { kind: "itx", options: Fo(t) };
        Ee(t, "Unknown transaction kind");
      }
    }
    __name($l, "$l");
    function Fo(t) {
      return { id: t.id, payload: t.payload };
    }
    __name(Fo, "Fo");
    function Vl(t, e) {
      return hr(t) && e?.kind === "batch" && t.batchRequestIdx !== e.index;
    }
    __name(Vl, "Vl");
    function jl(t) {
      return t.code === "P2009" || t.code === "P2012";
    }
    __name(jl, "jl");
    function Uo(t) {
      if (t.kind === "Union") return { kind: "Union", errors: t.errors.map(Uo) };
      if (Array.isArray(t.selectionPath)) {
        let [, ...e] = t.selectionPath;
        return { ...t, selectionPath: e };
      }
      return t;
    }
    __name(Uo, "Uo");
    u();
    c();
    m();
    p();
    d();
    l();
    var No = yo;
    u();
    c();
    m();
    p();
    d();
    l();
    var jo = ot($r());
    u();
    c();
    m();
    p();
    d();
    l();
    var _ = class extends Error {
      static {
        __name(this, "_");
      }
      constructor(e) {
        super(e + `
Read more at https://pris.ly/d/client-constructor`), this.name = "PrismaClientConstructorValidationError";
      }
      get [Symbol.toStringTag]() {
        return "PrismaClientConstructorValidationError";
      }
    };
    re(_, "PrismaClientConstructorValidationError");
    var qo = ["datasources", "datasourceUrl", "errorFormat", "adapter", "log", "transactionOptions", "omit", "__internal"];
    var Bo = ["pretty", "colorless", "minimal"];
    var $o = ["info", "query", "warn", "error"];
    var Ql = { datasources: /* @__PURE__ */ __name((t, { datasourceNames: e }) => {
      if (t) {
        if (typeof t != "object" || Array.isArray(t)) throw new _(`Invalid value ${JSON.stringify(t)} for "datasources" provided to PrismaClient constructor`);
        for (let [r, n] of Object.entries(t)) {
          if (!e.includes(r)) {
            let i = nt(r, e) || ` Available datasources: ${e.join(", ")}`;
            throw new _(`Unknown datasource ${r} provided to PrismaClient constructor.${i}`);
          }
          if (typeof n != "object" || Array.isArray(n)) throw new _(`Invalid value ${JSON.stringify(t)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);
          if (n && typeof n == "object") for (let [i, o] of Object.entries(n)) {
            if (i !== "url") throw new _(`Invalid value ${JSON.stringify(t)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);
            if (typeof o != "string") throw new _(`Invalid value ${JSON.stringify(o)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);
          }
        }
      }
    }, "datasources"), adapter: /* @__PURE__ */ __name((t, e) => {
      if (!t && Be(e.generator) === "client") throw new _('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');
      if (t === null) return;
      if (t === void 0) throw new _('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');
      if (!gr(e).includes("driverAdapters")) throw new _('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');
      if (Be(e.generator) === "binary") throw new _('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.');
    }, "adapter"), datasourceUrl: /* @__PURE__ */ __name((t) => {
      if (typeof t < "u" && typeof t != "string") throw new _(`Invalid value ${JSON.stringify(t)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`);
    }, "datasourceUrl"), errorFormat: /* @__PURE__ */ __name((t) => {
      if (t) {
        if (typeof t != "string") throw new _(`Invalid value ${JSON.stringify(t)} for "errorFormat" provided to PrismaClient constructor.`);
        if (!Bo.includes(t)) {
          let e = nt(t, Bo);
          throw new _(`Invalid errorFormat ${t} provided to PrismaClient constructor.${e}`);
        }
      }
    }, "errorFormat"), log: /* @__PURE__ */ __name((t) => {
      if (!t) return;
      if (!Array.isArray(t)) throw new _(`Invalid value ${JSON.stringify(t)} for "log" provided to PrismaClient constructor.`);
      function e(r) {
        if (typeof r == "string" && !$o.includes(r)) {
          let n = nt(r, $o);
          throw new _(`Invalid log level "${r}" provided to PrismaClient constructor.${n}`);
        }
      }
      __name(e, "e");
      for (let r of t) {
        e(r);
        let n = { level: e, emit: /* @__PURE__ */ __name((i) => {
          let o = ["stdout", "event"];
          if (!o.includes(i)) {
            let s = nt(i, o);
            throw new _(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`);
          }
        }, "emit") };
        if (r && typeof r == "object") for (let [i, o] of Object.entries(r)) if (n[i]) n[i](o);
        else throw new _(`Invalid property ${i} for "log" provided to PrismaClient constructor`);
      }
    }, "log"), transactionOptions: /* @__PURE__ */ __name((t) => {
      if (!t) return;
      let e = t.maxWait;
      if (e != null && e <= 0) throw new _(`Invalid value ${e} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);
      let r = t.timeout;
      if (r != null && r <= 0) throw new _(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`);
    }, "transactionOptions"), omit: /* @__PURE__ */ __name((t, e) => {
      if (typeof t != "object") throw new _('"omit" option is expected to be an object.');
      if (t === null) throw new _('"omit" option can not be `null`');
      let r = [];
      for (let [n, i] of Object.entries(t)) {
        let o = Gl(n, e.runtimeDataModel);
        if (!o) {
          r.push({ kind: "UnknownModel", modelKey: n });
          continue;
        }
        for (let [s, a] of Object.entries(i)) {
          let f = o.fields.find((y) => y.name === s);
          if (!f) {
            r.push({ kind: "UnknownField", modelKey: n, fieldName: s });
            continue;
          }
          if (f.relationName) {
            r.push({ kind: "RelationInOmit", modelKey: n, fieldName: s });
            continue;
          }
          typeof a != "boolean" && r.push({ kind: "InvalidFieldValue", modelKey: n, fieldName: s });
        }
      }
      if (r.length > 0) throw new _(Wl(t, r));
    }, "omit"), __internal: /* @__PURE__ */ __name((t) => {
      if (!t) return;
      let e = ["debug", "engine", "configOverride"];
      if (typeof t != "object") throw new _(`Invalid value ${JSON.stringify(t)} for "__internal" to PrismaClient constructor`);
      for (let [r] of Object.entries(t)) if (!e.includes(r)) {
        let n = nt(r, e);
        throw new _(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${n}`);
      }
    }, "__internal") };
    function Qo(t, e) {
      for (let [r, n] of Object.entries(t)) {
        if (!qo.includes(r)) {
          let i = nt(r, qo);
          throw new _(`Unknown property ${r} provided to PrismaClient constructor.${i}`);
        }
        Ql[r](n, e);
      }
      if (t.datasourceUrl && t.datasources) throw new _('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them');
    }
    __name(Qo, "Qo");
    function nt(t, e) {
      if (e.length === 0 || typeof t != "string") return "";
      let r = Jl(t, e);
      return r ? ` Did you mean "${r}"?` : "";
    }
    __name(nt, "nt");
    function Jl(t, e) {
      if (e.length === 0) return null;
      let r = e.map((i) => ({ value: i, distance: (0, jo.default)(t, i) }));
      r.sort((i, o) => i.distance < o.distance ? -1 : 1);
      let n = r[0];
      return n.distance < 3 ? n.value : null;
    }
    __name(Jl, "Jl");
    function Gl(t, e) {
      return Vo(e.models, t) ?? Vo(e.types, t);
    }
    __name(Gl, "Gl");
    function Vo(t, e) {
      let r = Object.keys(t).find((n) => Te(n) === e);
      if (r) return t[r];
    }
    __name(Vo, "Vo");
    function Wl(t, e) {
      let r = Ye(t);
      for (let o of e) switch (o.kind) {
        case "UnknownModel":
          r.arguments.getField(o.modelKey)?.markAsError(), r.addErrorMessage(() => `Unknown model name: ${o.modelKey}.`);
          break;
        case "UnknownField":
          r.arguments.getDeepField([o.modelKey, o.fieldName])?.markAsError(), r.addErrorMessage(() => `Model "${o.modelKey}" does not have a field named "${o.fieldName}".`);
          break;
        case "RelationInOmit":
          r.arguments.getDeepField([o.modelKey, o.fieldName])?.markAsError(), r.addErrorMessage(() => 'Relations are already excluded by default and can not be specified in "omit".');
          break;
        case "InvalidFieldValue":
          r.arguments.getDeepFieldValue([o.modelKey, o.fieldName])?.markAsError(), r.addErrorMessage(() => "Omit field option value must be a boolean.");
          break;
      }
      let { message: n, args: i } = Xt(r, "colorless");
      return `Error validating "omit" option:

${i}

${n}`;
    }
    __name(Wl, "Wl");
    u();
    c();
    m();
    p();
    d();
    l();
    function Jo(t) {
      return t.length === 0 ? Promise.resolve([]) : new Promise((e, r) => {
        let n = new Array(t.length), i = null, o = false, s = 0, a = /* @__PURE__ */ __name(() => {
          o || (s++, s === t.length && (o = true, i ? r(i) : e(n)));
        }, "a"), f = /* @__PURE__ */ __name((y) => {
          o || (o = true, r(y));
        }, "f");
        for (let y = 0; y < t.length; y++) t[y].then((C) => {
          n[y] = C, a();
        }, (C) => {
          if (!hr(C)) {
            f(C);
            return;
          }
          C.batchRequestIdx === y ? f(C) : (i || (i = C), a());
        });
      });
    }
    __name(Jo, "Jo");
    var Se = G("prisma:client");
    typeof globalThis == "object" && (globalThis.NODE_CLIENT = true);
    var Kl = { requestArgsToMiddlewareArgs: /* @__PURE__ */ __name((t) => t, "requestArgsToMiddlewareArgs"), middlewareArgsToRequestArgs: /* @__PURE__ */ __name((t) => t, "middlewareArgsToRequestArgs") };
    var Hl = Symbol.for("prisma.client.transaction.id");
    var zl = { id: 0, nextId() {
      return ++this.id;
    } };
    function Ko(t) {
      class e {
        static {
          __name(this, "e");
        }
        _originalClient = this;
        _runtimeDataModel;
        _requestHandler;
        _connectionPromise;
        _disconnectionPromise;
        _engineConfig;
        _accelerateEngineConfig;
        _clientVersion;
        _errorFormat;
        _tracingHelper;
        _middlewares = new yr();
        _previewFeatures;
        _activeProvider;
        _globalOmit;
        _extensions;
        _engine;
        _appliedParent;
        _createPrismaPromise = ln();
        constructor(n) {
          t = n?.__internal?.configOverride?.(t) ?? t, fo(t), n && Qo(n, t);
          let i = new sr().on("error", () => {
          });
          this._extensions = Xe.empty(), this._previewFeatures = gr(t), this._clientVersion = t.clientVersion ?? No, this._activeProvider = t.activeProvider, this._globalOmit = n?.omit, this._tracingHelper = Io();
          let o = t.relativeEnvPaths && { rootEnvPath: t.relativeEnvPaths.rootEnvPath && Nt.resolve(t.dirname, t.relativeEnvPaths.rootEnvPath), schemaEnvPath: t.relativeEnvPaths.schemaEnvPath && Nt.resolve(t.dirname, t.relativeEnvPaths.schemaEnvPath) }, s;
          if (n?.adapter) {
            s = n.adapter;
            let f = t.activeProvider === "postgresql" || t.activeProvider === "cockroachdb" ? "postgres" : t.activeProvider;
            if (s.provider !== f) throw new D(`The Driver Adapter \`${s.adapterName}\`, based on \`${s.provider}\`, is not compatible with the provider \`${f}\` specified in the Prisma schema.`, this._clientVersion);
            if (n.datasources || n.datasourceUrl !== void 0) throw new D("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.", this._clientVersion);
          }
          let a = t.injectableEdgeEnv?.();
          try {
            let f = n ?? {}, y = f.__internal ?? {}, C = y.debug === true;
            C && G.enable("prisma:client");
            let R = Nt.resolve(t.dirname, t.relativePath);
            qn.existsSync(R) || (R = t.dirname), Se("dirname", t.dirname), Se("relativePath", t.relativePath), Se("cwd", R);
            let O = y.engine || {};
            if (f.errorFormat ? this._errorFormat = f.errorFormat : g.env.NODE_ENV === "production" ? this._errorFormat = "minimal" : g.env.NO_COLOR ? this._errorFormat = "colorless" : this._errorFormat = "colorless", this._runtimeDataModel = t.runtimeDataModel, this._engineConfig = { cwd: R, dirname: t.dirname, enableDebugLogs: C, allowTriggerPanic: O.allowTriggerPanic, prismaPath: O.binaryPath ?? void 0, engineEndpoint: O.endpoint, generator: t.generator, showColors: this._errorFormat === "pretty", logLevel: f.log && Do(f.log), logQueries: f.log && !!(typeof f.log == "string" ? f.log === "query" : f.log.find((A) => typeof A == "string" ? A === "query" : A.level === "query")), env: a?.parsed ?? {}, flags: [], engineWasm: t.engineWasm, compilerWasm: t.compilerWasm, clientVersion: t.clientVersion, engineVersion: t.engineVersion, previewFeatures: this._previewFeatures, activeProvider: t.activeProvider, inlineSchema: t.inlineSchema, overrideDatasources: go(f, t.datasourceNames), inlineDatasources: t.inlineDatasources, inlineSchemaHash: t.inlineSchemaHash, tracingHelper: this._tracingHelper, transactionOptions: { maxWait: f.transactionOptions?.maxWait ?? 2e3, timeout: f.transactionOptions?.timeout ?? 5e3, isolationLevel: f.transactionOptions?.isolationLevel }, logEmitter: i, isBundled: t.isBundled, adapter: s }, this._accelerateEngineConfig = { ...this._engineConfig, accelerateUtils: { resolveDatasourceUrl: dr, getBatchRequestPayload: ur, prismaGraphQLToJSError: cr, PrismaClientUnknownRequestError: Q, PrismaClientInitializationError: D, PrismaClientKnownRequestError: Z, debug: G("prisma:client:accelerateEngine"), engineVersion: Wo.version, clientVersion: t.clientVersion } }, Se("clientVersion", t.clientVersion), this._engine = Eo(t, this._engineConfig), this._requestHandler = new Er(this, i), f.log) for (let A of f.log) {
              let I = typeof A == "string" ? A : A.emit === "stdout" ? A.level : null;
              I && this.$on(I, (k) => {
                ut.log(`${ut.tags[I] ?? ""}`, k.message || k.query);
              });
            }
          } catch (f) {
            throw f.clientVersion = this._clientVersion, f;
          }
          return this._appliedParent = Ct(this);
        }
        get [Symbol.toStringTag]() {
          return "PrismaClient";
        }
        $use(n) {
          this._middlewares.use(n);
        }
        $on(n, i) {
          return n === "beforeExit" ? this._engine.onBeforeExit(i) : n && this._engineConfig.logEmitter.on(n, i), this;
        }
        $connect() {
          try {
            return this._engine.start();
          } catch (n) {
            throw n.clientVersion = this._clientVersion, n;
          }
        }
        async $disconnect() {
          try {
            await this._engine.stop();
          } catch (n) {
            throw n.clientVersion = this._clientVersion, n;
          } finally {
            Nn();
          }
        }
        $executeRawInternal(n, i, o, s) {
          let a = this._activeProvider;
          return this._request({ action: "executeRaw", args: o, transaction: n, clientMethod: i, argsMapper: an({ clientMethod: i, activeProvider: a }), callsite: Re(this._errorFormat), dataPath: [], middlewareArgsMapper: s });
        }
        $executeRaw(n, ...i) {
          return this._createPrismaPromise((o) => {
            if (n.raw !== void 0 || n.sql !== void 0) {
              let [s, a] = Go(n, i);
              return sn(this._activeProvider, s.text, s.values, Array.isArray(n) ? "prisma.$executeRaw`<SQL>`" : "prisma.$executeRaw(sql`<SQL>`)"), this.$executeRawInternal(o, "$executeRaw", s, a);
            }
            throw new K("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n", { clientVersion: this._clientVersion });
          });
        }
        $executeRawUnsafe(n, ...i) {
          return this._createPrismaPromise((o) => (sn(this._activeProvider, n, i, "prisma.$executeRawUnsafe(<SQL>, [...values])"), this.$executeRawInternal(o, "$executeRawUnsafe", [n, ...i])));
        }
        $runCommandRaw(n) {
          if (t.activeProvider !== "mongodb") throw new K(`The ${t.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`, { clientVersion: this._clientVersion });
          return this._createPrismaPromise((i) => this._request({ args: n, clientMethod: "$runCommandRaw", dataPath: [], action: "runCommandRaw", argsMapper: xo, callsite: Re(this._errorFormat), transaction: i }));
        }
        async $queryRawInternal(n, i, o, s) {
          let a = this._activeProvider;
          return this._request({ action: "queryRaw", args: o, transaction: n, clientMethod: i, argsMapper: an({ clientMethod: i, activeProvider: a }), callsite: Re(this._errorFormat), dataPath: [], middlewareArgsMapper: s });
        }
        $queryRaw(n, ...i) {
          return this._createPrismaPromise((o) => {
            if (n.raw !== void 0 || n.sql !== void 0) return this.$queryRawInternal(o, "$queryRaw", ...Go(n, i));
            throw new K("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n", { clientVersion: this._clientVersion });
          });
        }
        $queryRawTyped(n) {
          return this._createPrismaPromise((i) => {
            if (!this._hasPreviewFlag("typedSql")) throw new K("`typedSql` preview feature must be enabled in order to access $queryRawTyped API", { clientVersion: this._clientVersion });
            return this.$queryRawInternal(i, "$queryRawTyped", n);
          });
        }
        $queryRawUnsafe(n, ...i) {
          return this._createPrismaPromise((o) => this.$queryRawInternal(o, "$queryRawUnsafe", [n, ...i]));
        }
        _transactionWithArray({ promises: n, options: i }) {
          let o = zl.nextId(), s = Mo(n.length), a = n.map((f, y) => {
            if (f?.[Symbol.toStringTag] !== "PrismaPromise") throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");
            let C = i?.isolationLevel ?? this._engineConfig.transactionOptions.isolationLevel, R = { kind: "batch", id: o, index: y, isolationLevel: C, lock: s };
            return f.requestTransaction?.(R) ?? f;
          });
          return Jo(a);
        }
        async _transactionWithCallback({ callback: n, options: i }) {
          let o = { traceparent: this._tracingHelper.getTraceParent() }, s = { maxWait: i?.maxWait ?? this._engineConfig.transactionOptions.maxWait, timeout: i?.timeout ?? this._engineConfig.transactionOptions.timeout, isolationLevel: i?.isolationLevel ?? this._engineConfig.transactionOptions.isolationLevel }, a = await this._engine.transaction("start", o, s), f;
          try {
            let y = { kind: "itx", ...a };
            f = await n(this._createItxClient(y)), await this._engine.transaction("commit", o, a);
          } catch (y) {
            throw await this._engine.transaction("rollback", o, a).catch(() => {
            }), y;
          }
          return f;
        }
        _createItxClient(n) {
          return le(Ct(le(Xi(this), [H("_appliedParent", () => this._appliedParent._createItxClient(n)), H("_createPrismaPromise", () => ln(n)), H(Hl, () => n.id)])), [et(no)]);
        }
        $transaction(n, i) {
          let o;
          typeof n == "function" ? this._engineConfig.adapter?.adapterName === "@prisma/adapter-d1" ? o = /* @__PURE__ */ __name(() => {
            throw new Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.");
          }, "o") : o = /* @__PURE__ */ __name(() => this._transactionWithCallback({ callback: n, options: i }), "o") : o = /* @__PURE__ */ __name(() => this._transactionWithArray({ promises: n, options: i }), "o");
          let s = { name: "transaction", attributes: { method: "$transaction" } };
          return this._tracingHelper.runInChildSpan(s, o);
        }
        _request(n) {
          n.otelParentCtx = this._tracingHelper.getActiveContext();
          let i = n.middlewareArgsMapper ?? Kl, o = { args: i.requestArgsToMiddlewareArgs(n.args), dataPath: n.dataPath, runInTransaction: !!n.transaction, action: n.action, model: n.model }, s = { middleware: { name: "middleware", middleware: true, attributes: { method: "$use" }, active: false }, operation: { name: "operation", attributes: { method: o.action, model: o.model, name: o.model ? `${o.model}.${o.action}` : o.action } } }, a = -1, f = /* @__PURE__ */ __name(async (y) => {
            let C = this._middlewares.get(++a);
            if (C) return this._tracingHelper.runInChildSpan(s.middleware, (M) => C(y, (se) => (M?.end(), f(se))));
            let { runInTransaction: R, args: O, ...A } = y, I = { ...n, ...A };
            O && (I.args = i.middlewareArgsToRequestArgs(O)), n.transaction !== void 0 && R === false && delete I.transaction;
            let k = await ao(this, I);
            return I.model ? ro({ result: k, modelName: I.model, args: I.args, extensions: this._extensions, runtimeDataModel: this._runtimeDataModel, globalOmit: this._globalOmit }) : k;
          }, "f");
          return this._tracingHelper.runInChildSpan(s.operation, () => f(o));
        }
        async _executeRequest({ args: n, clientMethod: i, dataPath: o, callsite: s, action: a, model: f, argsMapper: y, transaction: C, unpacker: R, otelParentCtx: O, customDataProxyFetch: A }) {
          try {
            n = y ? y(n) : n;
            let I = { name: "serialize" }, k = this._tracingHelper.runInChildSpan(I, () => nr({ modelName: f, runtimeDataModel: this._runtimeDataModel, action: a, args: n, clientMethod: i, callsite: s, extensions: this._extensions, errorFormat: this._errorFormat, clientVersion: this._clientVersion, previewFeatures: this._previewFeatures, globalOmit: this._globalOmit }));
            return G.enabled("prisma:client") && (Se("Prisma Client call:"), Se(`prisma.${i}(${Vi(n)})`), Se("Generated request:"), Se(JSON.stringify(k, null, 2) + `
`)), C?.kind === "batch" && await C.lock, this._requestHandler.request({ protocolQuery: k, modelName: f, action: a, clientMethod: i, dataPath: o, callsite: s, args: n, extensions: this._extensions, transaction: C, unpacker: R, otelParentCtx: O, otelChildCtx: this._tracingHelper.getActiveContext(), globalOmit: this._globalOmit, customDataProxyFetch: A });
          } catch (I) {
            throw I.clientVersion = this._clientVersion, I;
          }
        }
        $metrics = new Ze(this);
        _hasPreviewFlag(n) {
          return !!this._engineConfig.previewFeatures?.includes(n);
        }
        $applyPendingMigrations() {
          return this._engine.applyPendingMigrations();
        }
        $extends = Zi;
      }
      return e;
    }
    __name(Ko, "Ko");
    function Go(t, e) {
      return Yl(t) ? [new ee(t, e), So] : [t, ko];
    }
    __name(Go, "Go");
    function Yl(t) {
      return Array.isArray(t) && Array.isArray(t.raw);
    }
    __name(Yl, "Yl");
    u();
    c();
    m();
    p();
    d();
    l();
    var Xl = /* @__PURE__ */ new Set(["toJSON", "$$typeof", "asymmetricMatch", Symbol.iterator, Symbol.toStringTag, Symbol.isConcatSpreadable, Symbol.toPrimitive]);
    function Ho(t) {
      return new Proxy(t, { get(e, r) {
        if (r in e) return e[r];
        if (!Xl.has(r)) throw new TypeError(`Invalid enum value: ${String(r)}`);
      } });
    }
    __name(Ho, "Ho");
    u();
    c();
    m();
    p();
    d();
    l();
    l();
  }
});

// src/generated/prisma/query_engine_bg.js
var require_query_engine_bg = __commonJS({
  "src/generated/prisma/query_engine_bg.js"(exports, module) {
    "use strict";
    init_modules_watch_stub();
    var S = Object.defineProperty;
    var k = Object.getOwnPropertyDescriptor;
    var D = Object.getOwnPropertyNames;
    var R = Object.prototype.hasOwnProperty;
    var B = /* @__PURE__ */ __name((e, t) => {
      for (var n in t) S(e, n, { get: t[n], enumerable: true });
    }, "B");
    var U = /* @__PURE__ */ __name((e, t, n, o) => {
      if (t && typeof t == "object" || typeof t == "function") for (let _ of D(t)) !R.call(e, _) && _ !== n && S(e, _, { get: /* @__PURE__ */ __name(() => t[_], "get"), enumerable: !(o = k(t, _)) || o.enumerable });
      return e;
    }, "U");
    var L = /* @__PURE__ */ __name((e) => U(S({}, "__esModule", { value: true }), e), "L");
    var Et = {};
    B(Et, { QueryEngine: /* @__PURE__ */ __name(() => Q, "QueryEngine"), __wbg_String_8f0eb39a4a4c2f66: /* @__PURE__ */ __name(() => H, "__wbg_String_8f0eb39a4a4c2f66"), __wbg_buffer_609cc3eee51ed158: /* @__PURE__ */ __name(() => J, "__wbg_buffer_609cc3eee51ed158"), __wbg_call_672a4d21634d4a24: /* @__PURE__ */ __name(() => K, "__wbg_call_672a4d21634d4a24"), __wbg_call_7cccdd69e0791ae2: /* @__PURE__ */ __name(() => X, "__wbg_call_7cccdd69e0791ae2"), __wbg_crypto_805be4ce92f1e370: /* @__PURE__ */ __name(() => Y, "__wbg_crypto_805be4ce92f1e370"), __wbg_done_769e5ede4b31c67b: /* @__PURE__ */ __name(() => Z, "__wbg_done_769e5ede4b31c67b"), __wbg_entries_3265d4158b33e5dc: /* @__PURE__ */ __name(() => ee, "__wbg_entries_3265d4158b33e5dc"), __wbg_getRandomValues_f6a868620c8bab49: /* @__PURE__ */ __name(() => te, "__wbg_getRandomValues_f6a868620c8bab49"), __wbg_getTime_46267b1c24877e30: /* @__PURE__ */ __name(() => ne, "__wbg_getTime_46267b1c24877e30"), __wbg_get_67b2ba62fc30de12: /* @__PURE__ */ __name(() => re, "__wbg_get_67b2ba62fc30de12"), __wbg_get_b9b93047fe3cf45b: /* @__PURE__ */ __name(() => oe, "__wbg_get_b9b93047fe3cf45b"), __wbg_get_ece95cf6585650d9: /* @__PURE__ */ __name(() => _e, "__wbg_get_ece95cf6585650d9"), __wbg_getwithrefkey_1dc361bd10053bfe: /* @__PURE__ */ __name(() => ce, "__wbg_getwithrefkey_1dc361bd10053bfe"), __wbg_has_a5ea9117f258a0ec: /* @__PURE__ */ __name(() => ie, "__wbg_has_a5ea9117f258a0ec"), __wbg_instanceof_ArrayBuffer_e14585432e3737fc: /* @__PURE__ */ __name(() => ue, "__wbg_instanceof_ArrayBuffer_e14585432e3737fc"), __wbg_instanceof_Map_f3469ce2244d2430: /* @__PURE__ */ __name(() => se, "__wbg_instanceof_Map_f3469ce2244d2430"), __wbg_instanceof_Promise_935168b8f4b49db3: /* @__PURE__ */ __name(() => fe, "__wbg_instanceof_Promise_935168b8f4b49db3"), __wbg_instanceof_Uint8Array_17156bcf118086a9: /* @__PURE__ */ __name(() => ae, "__wbg_instanceof_Uint8Array_17156bcf118086a9"), __wbg_isArray_a1eab7e0d067391b: /* @__PURE__ */ __name(() => be, "__wbg_isArray_a1eab7e0d067391b"), __wbg_isSafeInteger_343e2beeeece1bb0: /* @__PURE__ */ __name(() => le, "__wbg_isSafeInteger_343e2beeeece1bb0"), __wbg_iterator_9a24c88df860dc65: /* @__PURE__ */ __name(() => ge, "__wbg_iterator_9a24c88df860dc65"), __wbg_keys_5c77a08ddc2fb8a6: /* @__PURE__ */ __name(() => de, "__wbg_keys_5c77a08ddc2fb8a6"), __wbg_length_a446193dc22c12f8: /* @__PURE__ */ __name(() => we, "__wbg_length_a446193dc22c12f8"), __wbg_length_e2d2a49132c1b256: /* @__PURE__ */ __name(() => pe, "__wbg_length_e2d2a49132c1b256"), __wbg_msCrypto_2ac4d17c4748234a: /* @__PURE__ */ __name(() => xe, "__wbg_msCrypto_2ac4d17c4748234a"), __wbg_new0_f788a2397c7ca929: /* @__PURE__ */ __name(() => ye, "__wbg_new0_f788a2397c7ca929"), __wbg_new_23a2665fac83c611: /* @__PURE__ */ __name(() => me, "__wbg_new_23a2665fac83c611"), __wbg_new_405e22f390576ce2: /* @__PURE__ */ __name(() => he, "__wbg_new_405e22f390576ce2"), __wbg_new_5e0be73521bc8c17: /* @__PURE__ */ __name(() => Te, "__wbg_new_5e0be73521bc8c17"), __wbg_new_78feb108b6472713: /* @__PURE__ */ __name(() => qe, "__wbg_new_78feb108b6472713"), __wbg_new_a12002a7f91c75be: /* @__PURE__ */ __name(() => Se, "__wbg_new_a12002a7f91c75be"), __wbg_newnoargs_105ed471475aaf50: /* @__PURE__ */ __name(() => Ae, "__wbg_newnoargs_105ed471475aaf50"), __wbg_newwithbyteoffsetandlength_d97e637ebe145a9a: /* @__PURE__ */ __name(() => Ie, "__wbg_newwithbyteoffsetandlength_d97e637ebe145a9a"), __wbg_newwithlength_a381634e90c276d4: /* @__PURE__ */ __name(() => Ee, "__wbg_newwithlength_a381634e90c276d4"), __wbg_next_25feadfc0913fea9: /* @__PURE__ */ __name(() => Oe, "__wbg_next_25feadfc0913fea9"), __wbg_next_6574e1a8a62d1055: /* @__PURE__ */ __name(() => Fe, "__wbg_next_6574e1a8a62d1055"), __wbg_node_ecc8306b9857f33d: /* @__PURE__ */ __name(() => Me, "__wbg_node_ecc8306b9857f33d"), __wbg_now_7fd00a794a07d388: /* @__PURE__ */ __name(() => je, "__wbg_now_7fd00a794a07d388"), __wbg_now_807e54c39636c349: /* @__PURE__ */ __name(() => ke, "__wbg_now_807e54c39636c349"), __wbg_now_b3f7572f6ef3d3a9: /* @__PURE__ */ __name(() => De, "__wbg_now_b3f7572f6ef3d3a9"), __wbg_process_5cff2739921be718: /* @__PURE__ */ __name(() => Re, "__wbg_process_5cff2739921be718"), __wbg_push_737cfc8c1432c2c6: /* @__PURE__ */ __name(() => Be, "__wbg_push_737cfc8c1432c2c6"), __wbg_queueMicrotask_5a8a9131f3f0b37b: /* @__PURE__ */ __name(() => Ue, "__wbg_queueMicrotask_5a8a9131f3f0b37b"), __wbg_queueMicrotask_6d79674585219521: /* @__PURE__ */ __name(() => Le, "__wbg_queueMicrotask_6d79674585219521"), __wbg_randomFillSync_d3c85af7e31cf1f8: /* @__PURE__ */ __name(() => ve, "__wbg_randomFillSync_d3c85af7e31cf1f8"), __wbg_require_0c566c6f2eef6c79: /* @__PURE__ */ __name(() => Ne, "__wbg_require_0c566c6f2eef6c79"), __wbg_resolve_4851785c9c5f573d: /* @__PURE__ */ __name(() => $e, "__wbg_resolve_4851785c9c5f573d"), __wbg_setTimeout_5d6a1d4fc51ea450: /* @__PURE__ */ __name(() => Ce, "__wbg_setTimeout_5d6a1d4fc51ea450"), __wbg_set_37837023f3d740e8: /* @__PURE__ */ __name(() => Ve, "__wbg_set_37837023f3d740e8"), __wbg_set_3f1d0b984ed272ed: /* @__PURE__ */ __name(() => ze, "__wbg_set_3f1d0b984ed272ed"), __wbg_set_65595bdd868b3009: /* @__PURE__ */ __name(() => We, "__wbg_set_65595bdd868b3009"), __wbg_set_8fc6bf8a5b1071d1: /* @__PURE__ */ __name(() => Pe, "__wbg_set_8fc6bf8a5b1071d1"), __wbg_set_bb8cecf6a62b9f46: /* @__PURE__ */ __name(() => Ge, "__wbg_set_bb8cecf6a62b9f46"), __wbg_set_wasm: /* @__PURE__ */ __name(() => v, "__wbg_set_wasm"), __wbg_static_accessor_GLOBAL_88a902d13a557d07: /* @__PURE__ */ __name(() => Qe, "__wbg_static_accessor_GLOBAL_88a902d13a557d07"), __wbg_static_accessor_GLOBAL_THIS_56578be7e9f832b0: /* @__PURE__ */ __name(() => He, "__wbg_static_accessor_GLOBAL_THIS_56578be7e9f832b0"), __wbg_static_accessor_SELF_37c5d418e4bf5819: /* @__PURE__ */ __name(() => Je, "__wbg_static_accessor_SELF_37c5d418e4bf5819"), __wbg_static_accessor_WINDOW_5de37043a91a9c40: /* @__PURE__ */ __name(() => Ke, "__wbg_static_accessor_WINDOW_5de37043a91a9c40"), __wbg_subarray_aa9065fa9dc5df96: /* @__PURE__ */ __name(() => Xe, "__wbg_subarray_aa9065fa9dc5df96"), __wbg_then_44b73946d2fb3e7d: /* @__PURE__ */ __name(() => Ye, "__wbg_then_44b73946d2fb3e7d"), __wbg_then_48b406749878a531: /* @__PURE__ */ __name(() => Ze, "__wbg_then_48b406749878a531"), __wbg_valueOf_7392193dd78c6b97: /* @__PURE__ */ __name(() => et, "__wbg_valueOf_7392193dd78c6b97"), __wbg_value_cd1ffa7b1ab794f1: /* @__PURE__ */ __name(() => tt, "__wbg_value_cd1ffa7b1ab794f1"), __wbg_versions_a8e5a362e1f16442: /* @__PURE__ */ __name(() => nt, "__wbg_versions_a8e5a362e1f16442"), __wbindgen_as_number: /* @__PURE__ */ __name(() => rt, "__wbindgen_as_number"), __wbindgen_bigint_from_i64: /* @__PURE__ */ __name(() => ot, "__wbindgen_bigint_from_i64"), __wbindgen_bigint_from_u64: /* @__PURE__ */ __name(() => _t, "__wbindgen_bigint_from_u64"), __wbindgen_bigint_get_as_i64: /* @__PURE__ */ __name(() => ct, "__wbindgen_bigint_get_as_i64"), __wbindgen_boolean_get: /* @__PURE__ */ __name(() => it, "__wbindgen_boolean_get"), __wbindgen_cb_drop: /* @__PURE__ */ __name(() => ut, "__wbindgen_cb_drop"), __wbindgen_closure_wrapper7413: /* @__PURE__ */ __name(() => st, "__wbindgen_closure_wrapper7413"), __wbindgen_debug_string: /* @__PURE__ */ __name(() => ft, "__wbindgen_debug_string"), __wbindgen_error_new: /* @__PURE__ */ __name(() => at, "__wbindgen_error_new"), __wbindgen_in: /* @__PURE__ */ __name(() => bt, "__wbindgen_in"), __wbindgen_init_externref_table: /* @__PURE__ */ __name(() => lt, "__wbindgen_init_externref_table"), __wbindgen_is_bigint: /* @__PURE__ */ __name(() => gt, "__wbindgen_is_bigint"), __wbindgen_is_function: /* @__PURE__ */ __name(() => dt, "__wbindgen_is_function"), __wbindgen_is_object: /* @__PURE__ */ __name(() => wt, "__wbindgen_is_object"), __wbindgen_is_string: /* @__PURE__ */ __name(() => pt, "__wbindgen_is_string"), __wbindgen_is_undefined: /* @__PURE__ */ __name(() => xt, "__wbindgen_is_undefined"), __wbindgen_jsval_eq: /* @__PURE__ */ __name(() => yt, "__wbindgen_jsval_eq"), __wbindgen_jsval_loose_eq: /* @__PURE__ */ __name(() => mt, "__wbindgen_jsval_loose_eq"), __wbindgen_memory: /* @__PURE__ */ __name(() => ht, "__wbindgen_memory"), __wbindgen_number_get: /* @__PURE__ */ __name(() => Tt, "__wbindgen_number_get"), __wbindgen_number_new: /* @__PURE__ */ __name(() => qt, "__wbindgen_number_new"), __wbindgen_string_get: /* @__PURE__ */ __name(() => St, "__wbindgen_string_get"), __wbindgen_string_new: /* @__PURE__ */ __name(() => At, "__wbindgen_string_new"), __wbindgen_throw: /* @__PURE__ */ __name(() => It, "__wbindgen_throw"), debug_panic: /* @__PURE__ */ __name(() => W, "debug_panic"), getBuildTimeInfo: /* @__PURE__ */ __name(() => z, "getBuildTimeInfo") });
    module.exports = L(Et);
    var y = /* @__PURE__ */ __name(() => {
    }, "y");
    y.prototype = y;
    var r;
    function v(e) {
      r = e;
    }
    __name(v, "v");
    var s = 0;
    var m = null;
    function h() {
      return (m === null || m.byteLength === 0) && (m = new Uint8Array(r.memory.buffer)), m;
    }
    __name(h, "h");
    var N = typeof TextEncoder > "u" ? (0, module.require)("util").TextEncoder : TextEncoder;
    var T = new N("utf-8");
    var $3 = typeof T.encodeInto == "function" ? function(e, t) {
      return T.encodeInto(e, t);
    } : function(e, t) {
      const n = T.encode(e);
      return t.set(n), { read: e.length, written: n.length };
    };
    function f(e, t, n) {
      if (n === void 0) {
        const u = T.encode(e), a = t(u.length, 1) >>> 0;
        return h().subarray(a, a + u.length).set(u), s = u.length, a;
      }
      let o = e.length, _ = t(o, 1) >>> 0;
      const i = h();
      let c = 0;
      for (; c < o; c++) {
        const u = e.charCodeAt(c);
        if (u > 127) break;
        i[_ + c] = u;
      }
      if (c !== o) {
        c !== 0 && (e = e.slice(c)), _ = n(_, o, o = c + e.length * 3, 1) >>> 0;
        const u = h().subarray(_ + c, _ + o), a = $3(e, u);
        c += a.written, _ = n(_, o, c, 1) >>> 0;
      }
      return s = c, _;
    }
    __name(f, "f");
    var w = null;
    function g() {
      return (w === null || w.buffer.detached === true || w.buffer.detached === void 0 && w.buffer !== r.memory.buffer) && (w = new DataView(r.memory.buffer)), w;
    }
    __name(g, "g");
    function p(e) {
      const t = r.__externref_table_alloc();
      return r.__wbindgen_export_4.set(t, e), t;
    }
    __name(p, "p");
    function b(e, t) {
      try {
        return e.apply(this, t);
      } catch (n) {
        const o = p(n);
        r.__wbindgen_exn_store(o);
      }
    }
    __name(b, "b");
    var C = typeof TextDecoder > "u" ? (0, module.require)("util").TextDecoder : TextDecoder;
    var I = new C("utf-8", { ignoreBOM: true, fatal: true });
    I.decode();
    function q(e, t) {
      return e = e >>> 0, I.decode(h().subarray(e, e + t));
    }
    __name(q, "q");
    function l(e) {
      return e == null;
    }
    __name(l, "l");
    var E = typeof FinalizationRegistry > "u" ? { register: /* @__PURE__ */ __name(() => {
    }, "register"), unregister: /* @__PURE__ */ __name(() => {
    }, "unregister") } : new FinalizationRegistry((e) => {
      r.__wbindgen_export_5.get(e.dtor)(e.a, e.b);
    });
    function V(e, t, n, o) {
      const _ = { a: e, b: t, cnt: 1, dtor: n }, i = /* @__PURE__ */ __name((...c) => {
        _.cnt++;
        const u = _.a;
        _.a = 0;
        try {
          return o(u, _.b, ...c);
        } finally {
          --_.cnt === 0 ? (r.__wbindgen_export_5.get(_.dtor)(u, _.b), E.unregister(_)) : _.a = u;
        }
      }, "i");
      return i.original = _, E.register(i, _, _), i;
    }
    __name(V, "V");
    function A(e) {
      const t = typeof e;
      if (t == "number" || t == "boolean" || e == null) return `${e}`;
      if (t == "string") return `"${e}"`;
      if (t == "symbol") {
        const _ = e.description;
        return _ == null ? "Symbol" : `Symbol(${_})`;
      }
      if (t == "function") {
        const _ = e.name;
        return typeof _ == "string" && _.length > 0 ? `Function(${_})` : "Function";
      }
      if (Array.isArray(e)) {
        const _ = e.length;
        let i = "[";
        _ > 0 && (i += A(e[0]));
        for (let c = 1; c < _; c++) i += ", " + A(e[c]);
        return i += "]", i;
      }
      const n = /\[object ([^\]]+)\]/.exec(toString.call(e));
      let o;
      if (n && n.length > 1) o = n[1];
      else return toString.call(e);
      if (o == "Object") try {
        return "Object(" + JSON.stringify(e) + ")";
      } catch {
        return "Object";
      }
      return e instanceof Error ? `${e.name}: ${e.message}
${e.stack}` : o;
    }
    __name(A, "A");
    function O(e) {
      const t = r.__wbindgen_export_4.get(e);
      return r.__externref_table_dealloc(e), t;
    }
    __name(O, "O");
    function z() {
      return r.getBuildTimeInfo();
    }
    __name(z, "z");
    function W(e) {
      var t = l(e) ? 0 : f(e, r.__wbindgen_malloc, r.__wbindgen_realloc), n = s;
      const o = r.debug_panic(t, n);
      if (o[1]) throw O(o[0]);
    }
    __name(W, "W");
    function P(e, t, n) {
      r.closure581_externref_shim(e, t, n);
    }
    __name(P, "P");
    function G(e, t, n, o) {
      r.closure130_externref_shim(e, t, n, o);
    }
    __name(G, "G");
    var F = typeof FinalizationRegistry > "u" ? { register: /* @__PURE__ */ __name(() => {
    }, "register"), unregister: /* @__PURE__ */ __name(() => {
    }, "unregister") } : new FinalizationRegistry((e) => r.__wbg_queryengine_free(e >>> 0, 1));
    var Q = class {
      static {
        __name(this, "Q");
      }
      __destroy_into_raw() {
        const t = this.__wbg_ptr;
        return this.__wbg_ptr = 0, F.unregister(this), t;
      }
      free() {
        const t = this.__destroy_into_raw();
        r.__wbg_queryengine_free(t, 0);
      }
      constructor(t, n, o) {
        const _ = r.queryengine_new(t, n, o);
        if (_[2]) throw O(_[1]);
        return this.__wbg_ptr = _[0] >>> 0, F.register(this, this.__wbg_ptr, this), this;
      }
      connect(t, n) {
        const o = f(t, r.__wbindgen_malloc, r.__wbindgen_realloc), _ = s, i = f(n, r.__wbindgen_malloc, r.__wbindgen_realloc), c = s;
        return r.queryengine_connect(this.__wbg_ptr, o, _, i, c);
      }
      disconnect(t, n) {
        const o = f(t, r.__wbindgen_malloc, r.__wbindgen_realloc), _ = s, i = f(n, r.__wbindgen_malloc, r.__wbindgen_realloc), c = s;
        return r.queryengine_disconnect(this.__wbg_ptr, o, _, i, c);
      }
      query(t, n, o, _) {
        const i = f(t, r.__wbindgen_malloc, r.__wbindgen_realloc), c = s, u = f(n, r.__wbindgen_malloc, r.__wbindgen_realloc), a = s;
        var d = l(o) ? 0 : f(o, r.__wbindgen_malloc, r.__wbindgen_realloc), x = s;
        const M = f(_, r.__wbindgen_malloc, r.__wbindgen_realloc), j = s;
        return r.queryengine_query(this.__wbg_ptr, i, c, u, a, d, x, M, j);
      }
      startTransaction(t, n, o) {
        const _ = f(t, r.__wbindgen_malloc, r.__wbindgen_realloc), i = s, c = f(n, r.__wbindgen_malloc, r.__wbindgen_realloc), u = s, a = f(o, r.__wbindgen_malloc, r.__wbindgen_realloc), d = s;
        return r.queryengine_startTransaction(this.__wbg_ptr, _, i, c, u, a, d);
      }
      commitTransaction(t, n, o) {
        const _ = f(t, r.__wbindgen_malloc, r.__wbindgen_realloc), i = s, c = f(n, r.__wbindgen_malloc, r.__wbindgen_realloc), u = s, a = f(o, r.__wbindgen_malloc, r.__wbindgen_realloc), d = s;
        return r.queryengine_commitTransaction(this.__wbg_ptr, _, i, c, u, a, d);
      }
      rollbackTransaction(t, n, o) {
        const _ = f(t, r.__wbindgen_malloc, r.__wbindgen_realloc), i = s, c = f(n, r.__wbindgen_malloc, r.__wbindgen_realloc), u = s, a = f(o, r.__wbindgen_malloc, r.__wbindgen_realloc), d = s;
        return r.queryengine_rollbackTransaction(this.__wbg_ptr, _, i, c, u, a, d);
      }
      metrics(t) {
        const n = f(t, r.__wbindgen_malloc, r.__wbindgen_realloc), o = s;
        return r.queryengine_metrics(this.__wbg_ptr, n, o);
      }
      trace(t) {
        const n = f(t, r.__wbindgen_malloc, r.__wbindgen_realloc), o = s;
        return r.queryengine_trace(this.__wbg_ptr, n, o);
      }
    };
    function H(e, t) {
      const n = String(t), o = f(n, r.__wbindgen_malloc, r.__wbindgen_realloc), _ = s;
      g().setInt32(e + 4 * 1, _, true), g().setInt32(e + 4 * 0, o, true);
    }
    __name(H, "H");
    function J(e) {
      return e.buffer;
    }
    __name(J, "J");
    function K() {
      return b(function(e, t) {
        return e.call(t);
      }, arguments);
    }
    __name(K, "K");
    function X() {
      return b(function(e, t, n) {
        return e.call(t, n);
      }, arguments);
    }
    __name(X, "X");
    function Y(e) {
      return e.crypto;
    }
    __name(Y, "Y");
    function Z(e) {
      return e.done;
    }
    __name(Z, "Z");
    function ee(e) {
      return Object.entries(e);
    }
    __name(ee, "ee");
    function te() {
      return b(function(e, t) {
        e.getRandomValues(t);
      }, arguments);
    }
    __name(te, "te");
    function ne(e) {
      return e.getTime();
    }
    __name(ne, "ne");
    function re() {
      return b(function(e, t) {
        return Reflect.get(e, t);
      }, arguments);
    }
    __name(re, "re");
    function oe(e, t) {
      return e[t >>> 0];
    }
    __name(oe, "oe");
    function _e() {
      return b(function(e, t) {
        return e[t];
      }, arguments);
    }
    __name(_e, "_e");
    function ce(e, t) {
      return e[t];
    }
    __name(ce, "ce");
    function ie() {
      return b(function(e, t) {
        return Reflect.has(e, t);
      }, arguments);
    }
    __name(ie, "ie");
    function ue(e) {
      let t;
      try {
        t = e instanceof ArrayBuffer;
      } catch {
        t = false;
      }
      return t;
    }
    __name(ue, "ue");
    function se(e) {
      let t;
      try {
        t = e instanceof Map;
      } catch {
        t = false;
      }
      return t;
    }
    __name(se, "se");
    function fe(e) {
      let t;
      try {
        t = e instanceof Promise;
      } catch {
        t = false;
      }
      return t;
    }
    __name(fe, "fe");
    function ae(e) {
      let t;
      try {
        t = e instanceof Uint8Array;
      } catch {
        t = false;
      }
      return t;
    }
    __name(ae, "ae");
    function be(e) {
      return Array.isArray(e);
    }
    __name(be, "be");
    function le(e) {
      return Number.isSafeInteger(e);
    }
    __name(le, "le");
    function ge() {
      return Symbol.iterator;
    }
    __name(ge, "ge");
    function de(e) {
      return Object.keys(e);
    }
    __name(de, "de");
    function we(e) {
      return e.length;
    }
    __name(we, "we");
    function pe(e) {
      return e.length;
    }
    __name(pe, "pe");
    function xe(e) {
      return e.msCrypto;
    }
    __name(xe, "xe");
    function ye() {
      return /* @__PURE__ */ new Date();
    }
    __name(ye, "ye");
    function me(e, t) {
      try {
        var n = { a: e, b: t }, o = /* @__PURE__ */ __name((i, c) => {
          const u = n.a;
          n.a = 0;
          try {
            return G(u, n.b, i, c);
          } finally {
            n.a = u;
          }
        }, "o");
        return new Promise(o);
      } finally {
        n.a = n.b = 0;
      }
    }
    __name(me, "me");
    function he() {
      return new Object();
    }
    __name(he, "he");
    function Te() {
      return /* @__PURE__ */ new Map();
    }
    __name(Te, "Te");
    function qe() {
      return new Array();
    }
    __name(qe, "qe");
    function Se(e) {
      return new Uint8Array(e);
    }
    __name(Se, "Se");
    function Ae(e, t) {
      return new y(q(e, t));
    }
    __name(Ae, "Ae");
    function Ie(e, t, n) {
      return new Uint8Array(e, t >>> 0, n >>> 0);
    }
    __name(Ie, "Ie");
    function Ee(e) {
      return new Uint8Array(e >>> 0);
    }
    __name(Ee, "Ee");
    function Oe(e) {
      return e.next;
    }
    __name(Oe, "Oe");
    function Fe() {
      return b(function(e) {
        return e.next();
      }, arguments);
    }
    __name(Fe, "Fe");
    function Me(e) {
      return e.node;
    }
    __name(Me, "Me");
    function je(e) {
      return e.now();
    }
    __name(je, "je");
    function ke() {
      return Date.now();
    }
    __name(ke, "ke");
    function De() {
      return b(function() {
        return Date.now();
      }, arguments);
    }
    __name(De, "De");
    function Re(e) {
      return e.process;
    }
    __name(Re, "Re");
    function Be(e, t) {
      return e.push(t);
    }
    __name(Be, "Be");
    function Ue(e) {
      return e.queueMicrotask;
    }
    __name(Ue, "Ue");
    function Le(e) {
      queueMicrotask(e);
    }
    __name(Le, "Le");
    function ve() {
      return b(function(e, t) {
        e.randomFillSync(t);
      }, arguments);
    }
    __name(ve, "ve");
    function Ne() {
      return b(function() {
        return module.require;
      }, arguments);
    }
    __name(Ne, "Ne");
    function $e(e) {
      return Promise.resolve(e);
    }
    __name($e, "$e");
    function Ce(e, t) {
      return setTimeout(e, t >>> 0);
    }
    __name(Ce, "Ce");
    function Ve(e, t, n) {
      e[t >>> 0] = n;
    }
    __name(Ve, "Ve");
    function ze(e, t, n) {
      e[t] = n;
    }
    __name(ze, "ze");
    function We(e, t, n) {
      e.set(t, n >>> 0);
    }
    __name(We, "We");
    function Pe(e, t, n) {
      return e.set(t, n);
    }
    __name(Pe, "Pe");
    function Ge() {
      return b(function(e, t, n) {
        return Reflect.set(e, t, n);
      }, arguments);
    }
    __name(Ge, "Ge");
    function Qe() {
      const e = typeof global > "u" ? null : global;
      return l(e) ? 0 : p(e);
    }
    __name(Qe, "Qe");
    function He() {
      const e = typeof globalThis > "u" ? null : globalThis;
      return l(e) ? 0 : p(e);
    }
    __name(He, "He");
    function Je() {
      const e = typeof self > "u" ? null : self;
      return l(e) ? 0 : p(e);
    }
    __name(Je, "Je");
    function Ke() {
      const e = typeof window > "u" ? null : window;
      return l(e) ? 0 : p(e);
    }
    __name(Ke, "Ke");
    function Xe(e, t, n) {
      return e.subarray(t >>> 0, n >>> 0);
    }
    __name(Xe, "Xe");
    function Ye(e, t) {
      return e.then(t);
    }
    __name(Ye, "Ye");
    function Ze(e, t, n) {
      return e.then(t, n);
    }
    __name(Ze, "Ze");
    function et(e) {
      return e.valueOf();
    }
    __name(et, "et");
    function tt(e) {
      return e.value;
    }
    __name(tt, "tt");
    function nt(e) {
      return e.versions;
    }
    __name(nt, "nt");
    function rt(e) {
      return +e;
    }
    __name(rt, "rt");
    function ot(e) {
      return e;
    }
    __name(ot, "ot");
    function _t(e) {
      return BigInt.asUintN(64, e);
    }
    __name(_t, "_t");
    function ct(e, t) {
      const n = t, o = typeof n == "bigint" ? n : void 0;
      g().setBigInt64(e + 8 * 1, l(o) ? BigInt(0) : o, true), g().setInt32(e + 4 * 0, !l(o), true);
    }
    __name(ct, "ct");
    function it(e) {
      const t = e;
      return typeof t == "boolean" ? t ? 1 : 0 : 2;
    }
    __name(it, "it");
    function ut(e) {
      const t = e.original;
      return t.cnt-- == 1 ? (t.a = 0, true) : false;
    }
    __name(ut, "ut");
    function st(e, t, n) {
      return V(e, t, 582, P);
    }
    __name(st, "st");
    function ft(e, t) {
      const n = A(t), o = f(n, r.__wbindgen_malloc, r.__wbindgen_realloc), _ = s;
      g().setInt32(e + 4 * 1, _, true), g().setInt32(e + 4 * 0, o, true);
    }
    __name(ft, "ft");
    function at(e, t) {
      return new Error(q(e, t));
    }
    __name(at, "at");
    function bt(e, t) {
      return e in t;
    }
    __name(bt, "bt");
    function lt() {
      const e = r.__wbindgen_export_4, t = e.grow(4);
      e.set(0, void 0), e.set(t + 0, void 0), e.set(t + 1, null), e.set(t + 2, true), e.set(t + 3, false);
    }
    __name(lt, "lt");
    function gt(e) {
      return typeof e == "bigint";
    }
    __name(gt, "gt");
    function dt(e) {
      return typeof e == "function";
    }
    __name(dt, "dt");
    function wt(e) {
      const t = e;
      return typeof t == "object" && t !== null;
    }
    __name(wt, "wt");
    function pt(e) {
      return typeof e == "string";
    }
    __name(pt, "pt");
    function xt(e) {
      return e === void 0;
    }
    __name(xt, "xt");
    function yt(e, t) {
      return e === t;
    }
    __name(yt, "yt");
    function mt(e, t) {
      return e == t;
    }
    __name(mt, "mt");
    function ht() {
      return r.memory;
    }
    __name(ht, "ht");
    function Tt(e, t) {
      const n = t, o = typeof n == "number" ? n : void 0;
      g().setFloat64(e + 8 * 1, l(o) ? 0 : o, true), g().setInt32(e + 4 * 0, !l(o), true);
    }
    __name(Tt, "Tt");
    function qt(e) {
      return e;
    }
    __name(qt, "qt");
    function St(e, t) {
      const n = t, o = typeof n == "string" ? n : void 0;
      var _ = l(o) ? 0 : f(o, r.__wbindgen_malloc, r.__wbindgen_realloc), i = s;
      g().setInt32(e + 4 * 1, i, true), g().setInt32(e + 4 * 0, _, true);
    }
    __name(St, "St");
    function At(e, t) {
      return q(e, t);
    }
    __name(At, "At");
    function It(e, t) {
      throw new Error(q(e, t));
    }
    __name(It, "It");
  }
});

// src/generated/prisma/wasm-worker-loader.mjs
var wasm_worker_loader_exports = {};
__export(wasm_worker_loader_exports, {
  default: () => wasm_worker_loader_default
});
var wasm_worker_loader_default;
var init_wasm_worker_loader = __esm({
  "src/generated/prisma/wasm-worker-loader.mjs"() {
    "use strict";
    init_modules_watch_stub();
    wasm_worker_loader_default = import("./4747265335faebdb550e7f54eb3996472ba4ee30-query_engine_bg.wasm");
  }
});

// src/generated/prisma/wasm.js
var require_wasm = __commonJS({
  "src/generated/prisma/wasm.js"(exports) {
    "use strict";
    init_modules_watch_stub();
    Object.defineProperty(exports, "__esModule", { value: true });
    var {
      PrismaClientKnownRequestError: PrismaClientKnownRequestError2,
      PrismaClientUnknownRequestError: PrismaClientUnknownRequestError2,
      PrismaClientRustPanicError: PrismaClientRustPanicError2,
      PrismaClientInitializationError: PrismaClientInitializationError2,
      PrismaClientValidationError: PrismaClientValidationError2,
      getPrismaClient: getPrismaClient2,
      sqltag: sqltag2,
      empty: empty2,
      join: join2,
      raw: raw3,
      skip: skip2,
      Decimal: Decimal2,
      Debug: Debug3,
      objectEnumValues: objectEnumValues2,
      makeStrictEnum: makeStrictEnum2,
      Extensions: Extensions2,
      warnOnce: warnOnce2,
      defineDmmfProperty: defineDmmfProperty2,
      Public: Public2,
      getRuntime: getRuntime2,
      createParam: createParam2
    } = require_wasm_engine_edge();
    var Prisma = {};
    exports.Prisma = Prisma;
    exports.$Enums = {};
    Prisma.prismaVersion = {
      client: "6.13.0",
      engine: "361e86d0ea4987e9f53a565309b3eed797a6bcbd"
    };
    Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError2;
    Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError2;
    Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError2;
    Prisma.PrismaClientInitializationError = PrismaClientInitializationError2;
    Prisma.PrismaClientValidationError = PrismaClientValidationError2;
    Prisma.Decimal = Decimal2;
    Prisma.sql = sqltag2;
    Prisma.empty = empty2;
    Prisma.join = join2;
    Prisma.raw = raw3;
    Prisma.validator = Public2.validator;
    Prisma.getExtensionContext = Extensions2.getExtensionContext;
    Prisma.defineExtension = Extensions2.defineExtension;
    Prisma.DbNull = objectEnumValues2.instances.DbNull;
    Prisma.JsonNull = objectEnumValues2.instances.JsonNull;
    Prisma.AnyNull = objectEnumValues2.instances.AnyNull;
    Prisma.NullTypes = {
      DbNull: objectEnumValues2.classes.DbNull,
      JsonNull: objectEnumValues2.classes.JsonNull,
      AnyNull: objectEnumValues2.classes.AnyNull
    };
    exports.Prisma.TransactionIsolationLevel = makeStrictEnum2({
      Serializable: "Serializable"
    });
    exports.Prisma.UserScalarFieldEnum = {
      id: "id",
      username: "username",
      passwordHash: "passwordHash",
      role: "role",
      status: "status",
      nickName: "nickName",
      wechat: "wechat",
      avatar: "avatar",
      createdAt: "createdAt",
      updatedAt: "updatedAt"
    };
    exports.Prisma.ProductScalarFieldEnum = {
      id: "id",
      name: "name",
      description: "description",
      category: "category",
      status: "status",
      createdAt: "createdAt",
      updatedAt: "updatedAt"
    };
    exports.Prisma.ProductVersionScalarFieldEnum = {
      id: "id",
      productId: "productId",
      version: "version",
      versionName: "versionName",
      description: "description",
      verifyTemplate: "verifyTemplate",
      encryptionKey: "encryptionKey",
      defaultPrice: "defaultPrice",
      downloadLink: "downloadLink",
      coverUrl: "coverUrl",
      changelog: "changelog",
      status: "status",
      createdAt: "createdAt",
      updatedAt: "updatedAt"
    };
    exports.Prisma.AuthorizationScalarFieldEnum = {
      id: "id",
      distributorId: "distributorId",
      versionId: "versionId",
      customPrice: "customPrice",
      status: "status",
      createdAt: "createdAt",
      updatedAt: "updatedAt"
    };
    exports.Prisma.LicenseScalarFieldEnum = {
      id: "id",
      versionId: "versionId",
      licenseKey: "licenseKey",
      status: "status",
      verifyInstance: "verifyInstance",
      activatedAt: "activatedAt",
      distributorId: "distributorId",
      createdAt: "createdAt",
      updatedAt: "updatedAt"
    };
    exports.Prisma.SortOrder = {
      asc: "asc",
      desc: "desc"
    };
    exports.Prisma.NullsOrder = {
      first: "first",
      last: "last"
    };
    exports.UserRole = exports.$Enums.UserRole = {
      ADMIN: "ADMIN",
      DISTRIBUTOR: "DISTRIBUTOR"
    };
    exports.UserStatus = exports.$Enums.UserStatus = {
      ACTIVE: "ACTIVE",
      INACTIVE: "INACTIVE"
    };
    exports.ProductStatus = exports.$Enums.ProductStatus = {
      ACTIVE: "ACTIVE",
      INACTIVE: "INACTIVE"
    };
    exports.AuthorizationStatus = exports.$Enums.AuthorizationStatus = {
      ACTIVE: "ACTIVE",
      INACTIVE: "INACTIVE"
    };
    exports.LicenseStatus = exports.$Enums.LicenseStatus = {
      INACTIVE: "INACTIVE",
      ACTIVE: "ACTIVE",
      REVOKED: "REVOKED"
    };
    exports.Prisma.ModelName = {
      User: "User",
      Product: "Product",
      ProductVersion: "ProductVersion",
      Authorization: "Authorization",
      License: "License"
    };
    var config = {
      "generator": {
        "name": "client",
        "provider": {
          "fromEnvVar": null,
          "value": "prisma-client-js"
        },
        "output": {
          "value": "C:\\Users\\<USER>\\code\\verify\\backend\\src\\generated\\prisma",
          "fromEnvVar": null
        },
        "config": {
          "engineType": "library"
        },
        "binaryTargets": [
          {
            "fromEnvVar": null,
            "value": "windows",
            "native": true
          }
        ],
        "previewFeatures": [
          "driverAdapters"
        ],
        "sourceFilePath": "C:\\Users\\<USER>\\code\\verify\\backend\\prisma\\schema.prisma",
        "isCustomOutput": true
      },
      "relativeEnvPaths": {
        "rootEnvPath": null,
        "schemaEnvPath": "../../../.env"
      },
      "relativePath": "../../../prisma",
      "clientVersion": "6.13.0",
      "engineVersion": "361e86d0ea4987e9f53a565309b3eed797a6bcbd",
      "datasourceNames": [
        "db"
      ],
      "activeProvider": "sqlite",
      "postinstall": false,
      "inlineDatasources": {
        "db": {
          "url": {
            "fromEnvVar": "DATABASE_URL",
            "value": null
          }
        }
      },
      "inlineSchema": '// This is your Prisma schema file,\n// learn more about it in the docs: https://pris.ly/d/prisma-schema\n\ngenerator client {\n  provider        = "prisma-client-js"\n  output          = "../src/generated/prisma"\n  previewFeatures = ["driverAdapters"]\n}\n\ndatasource db {\n  provider = "sqlite"\n  url      = env("DATABASE_URL")\n}\n\n// \u7528\u6237\u8868\nmodel User {\n  id           Int        @id @default(autoincrement())\n  username     String     @unique\n  passwordHash String\n  role         UserRole\n  status       UserStatus @default(ACTIVE)\n\n  // \u5206\u53D1\u5546\u57FA\u672C\u4FE1\u606F\n  nickName String?\n  wechat   String?\n  avatar   String?\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // \u5173\u7CFB\n  licenses       License[]\n  authorizations Authorization[]\n\n  @@index([role])\n  @@index([status])\n  @@index([createdAt])\n  @@index([role, status])\n  @@map("users")\n}\n\n// \u4EA7\u54C1\u8868\uFF08\u57FA\u7840\u4FE1\u606F\uFF09\nmodel Product {\n  id          Int           @id @default(autoincrement())\n  name        String        @unique\n  description String?\n  category    String?\n  status      ProductStatus @default(ACTIVE)\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // \u5173\u7CFB\n  versions ProductVersion[]\n\n  @@index([status])\n  @@index([category])\n  @@index([createdAt])\n  @@map("products")\n}\n\n// \u4EA7\u54C1\u7248\u672C\u8868\uFF08\u5305\u542B\u9ED8\u8BA4\u9A8C\u8BC1\u6A21\u677F\u548C\u52A0\u5BC6\u5BC6\u94A5\uFF09\nmodel ProductVersion {\n  id             Int           @id @default(autoincrement())\n  productId      Int\n  version        String\n  versionName    String?\n  description    String?\n  verifyTemplate String // JSON\u683C\u5F0F\n  encryptionKey  String\n  defaultPrice   Float\n  downloadLink   String?\n  coverUrl       String?\n  changelog      String?\n  status         ProductStatus @default(ACTIVE)\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // \u5173\u7CFB\n  product        Product         @relation(fields: [productId], references: [id], onDelete: Cascade)\n  licenses       License[]\n  authorizations Authorization[]\n\n  @@unique([productId, version])\n  @@index([productId])\n  @@index([status])\n  @@index([createdAt])\n  @@index([productId, status])\n  @@map("product_versions")\n}\n\n// \u5206\u53D1\u5546\u6388\u6743\u8868\uFF08\u5E97\u94FA\u5546\u54C1\u6388\u6743\uFF09\nmodel Authorization {\n  id            Int                 @id @default(autoincrement())\n  distributorId Int\n  versionId     Int\n  customPrice   Float?\n  status        AuthorizationStatus @default(ACTIVE)\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // \u5173\u7CFB\n  distributor User           @relation(fields: [distributorId], references: [id], onDelete: Cascade)\n  version     ProductVersion @relation(fields: [versionId], references: [id], onDelete: Cascade)\n\n  @@unique([distributorId, versionId])\n  @@index([distributorId])\n  @@index([versionId])\n  @@index([status])\n  @@index([createdAt])\n  @@index([distributorId, status])\n  @@map("authorizations")\n}\n\n// \u8BB8\u53EF\u8BC1\u8868\nmodel License {\n  id             Int           @id @default(autoincrement())\n  versionId      Int\n  licenseKey     String        @unique\n  status         LicenseStatus @default(INACTIVE)\n  verifyInstance String?\n  activatedAt    DateTime?\n  distributorId  Int\n\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n\n  // \u5173\u7CFB\n  version     ProductVersion @relation(fields: [versionId], references: [id], onDelete: Cascade)\n  distributor User           @relation(fields: [distributorId], references: [id], onDelete: Restrict)\n\n  @@index([licenseKey])\n  @@index([versionId])\n  @@index([distributorId])\n  @@index([status])\n  @@index([activatedAt])\n  @@index([createdAt])\n  @@index([distributorId, status])\n  @@index([status, createdAt])\n  @@map("licenses")\n}\n\n// \u679A\u4E3E\u7C7B\u578B\u5B9A\u4E49\nenum UserRole {\n  ADMIN\n  DISTRIBUTOR\n}\n\nenum UserStatus {\n  ACTIVE\n  INACTIVE\n}\n\nenum ProductStatus {\n  ACTIVE\n  INACTIVE\n}\n\nenum AuthorizationStatus {\n  ACTIVE\n  INACTIVE\n}\n\nenum LicenseStatus {\n  INACTIVE\n  ACTIVE\n  REVOKED\n}\n',
      "inlineSchemaHash": "fd5666af64982fb6d123fdfb752e190c9a4134ca3e54a641e906279631f947db",
      "copyEngine": true
    };
    config.dirname = "/";
    config.runtimeDataModel = JSON.parse('{"models":{"User":{"fields":[{"name":"id","kind":"scalar","type":"Int"},{"name":"username","kind":"scalar","type":"String"},{"name":"passwordHash","kind":"scalar","type":"String"},{"name":"role","kind":"enum","type":"UserRole"},{"name":"status","kind":"enum","type":"UserStatus"},{"name":"nickName","kind":"scalar","type":"String"},{"name":"wechat","kind":"scalar","type":"String"},{"name":"avatar","kind":"scalar","type":"String"},{"name":"createdAt","kind":"scalar","type":"DateTime"},{"name":"updatedAt","kind":"scalar","type":"DateTime"},{"name":"licenses","kind":"object","type":"License","relationName":"LicenseToUser"},{"name":"authorizations","kind":"object","type":"Authorization","relationName":"AuthorizationToUser"}],"dbName":"users"},"Product":{"fields":[{"name":"id","kind":"scalar","type":"Int"},{"name":"name","kind":"scalar","type":"String"},{"name":"description","kind":"scalar","type":"String"},{"name":"category","kind":"scalar","type":"String"},{"name":"status","kind":"enum","type":"ProductStatus"},{"name":"createdAt","kind":"scalar","type":"DateTime"},{"name":"updatedAt","kind":"scalar","type":"DateTime"},{"name":"versions","kind":"object","type":"ProductVersion","relationName":"ProductToProductVersion"}],"dbName":"products"},"ProductVersion":{"fields":[{"name":"id","kind":"scalar","type":"Int"},{"name":"productId","kind":"scalar","type":"Int"},{"name":"version","kind":"scalar","type":"String"},{"name":"versionName","kind":"scalar","type":"String"},{"name":"description","kind":"scalar","type":"String"},{"name":"verifyTemplate","kind":"scalar","type":"String"},{"name":"encryptionKey","kind":"scalar","type":"String"},{"name":"defaultPrice","kind":"scalar","type":"Float"},{"name":"downloadLink","kind":"scalar","type":"String"},{"name":"coverUrl","kind":"scalar","type":"String"},{"name":"changelog","kind":"scalar","type":"String"},{"name":"status","kind":"enum","type":"ProductStatus"},{"name":"createdAt","kind":"scalar","type":"DateTime"},{"name":"updatedAt","kind":"scalar","type":"DateTime"},{"name":"product","kind":"object","type":"Product","relationName":"ProductToProductVersion"},{"name":"licenses","kind":"object","type":"License","relationName":"LicenseToProductVersion"},{"name":"authorizations","kind":"object","type":"Authorization","relationName":"AuthorizationToProductVersion"}],"dbName":"product_versions"},"Authorization":{"fields":[{"name":"id","kind":"scalar","type":"Int"},{"name":"distributorId","kind":"scalar","type":"Int"},{"name":"versionId","kind":"scalar","type":"Int"},{"name":"customPrice","kind":"scalar","type":"Float"},{"name":"status","kind":"enum","type":"AuthorizationStatus"},{"name":"createdAt","kind":"scalar","type":"DateTime"},{"name":"updatedAt","kind":"scalar","type":"DateTime"},{"name":"distributor","kind":"object","type":"User","relationName":"AuthorizationToUser"},{"name":"version","kind":"object","type":"ProductVersion","relationName":"AuthorizationToProductVersion"}],"dbName":"authorizations"},"License":{"fields":[{"name":"id","kind":"scalar","type":"Int"},{"name":"versionId","kind":"scalar","type":"Int"},{"name":"licenseKey","kind":"scalar","type":"String"},{"name":"status","kind":"enum","type":"LicenseStatus"},{"name":"verifyInstance","kind":"scalar","type":"String"},{"name":"activatedAt","kind":"scalar","type":"DateTime"},{"name":"distributorId","kind":"scalar","type":"Int"},{"name":"createdAt","kind":"scalar","type":"DateTime"},{"name":"updatedAt","kind":"scalar","type":"DateTime"},{"name":"version","kind":"object","type":"ProductVersion","relationName":"LicenseToProductVersion"},{"name":"distributor","kind":"object","type":"User","relationName":"LicenseToUser"}],"dbName":"licenses"}},"enums":{},"types":{}}');
    defineDmmfProperty2(exports.Prisma, config.runtimeDataModel);
    config.engineWasm = {
      getRuntime: /* @__PURE__ */ __name(async () => require_query_engine_bg(), "getRuntime"),
      getQueryEngineWasmModule: /* @__PURE__ */ __name(async () => {
        const loader = (await Promise.resolve().then(() => (init_wasm_worker_loader(), wasm_worker_loader_exports))).default;
        const engine = (await loader).default;
        return engine;
      }, "getQueryEngineWasmModule")
    };
    config.compilerWasm = void 0;
    config.injectableEdgeEnv = () => ({
      parsed: {
        DATABASE_URL: typeof globalThis !== "undefined" && globalThis["DATABASE_URL"] || typeof process !== "undefined" && process.env && process.env.DATABASE_URL || void 0
      }
    });
    if (typeof globalThis !== "undefined" && globalThis["DEBUG"] || typeof process !== "undefined" && process.env && process.env.DEBUG || void 0) {
      Debug3.enable(typeof globalThis !== "undefined" && globalThis["DEBUG"] || typeof process !== "undefined" && process.env && process.env.DEBUG || void 0);
    }
    var PrismaClient3 = getPrismaClient2(config);
    exports.PrismaClient = PrismaClient3;
    Object.assign(exports, Prisma);
  }
});

// src/generated/prisma/default.js
var require_default = __commonJS({
  "src/generated/prisma/default.js"(exports, module) {
    "use strict";
    init_modules_watch_stub();
    module.exports = { ...require_wasm() };
  }
});

// .wrangler/tmp/bundle-4PegfM/middleware-loader.entry.ts
init_modules_watch_stub();

// .wrangler/tmp/bundle-4PegfM/middleware-insertion-facade.js
init_modules_watch_stub();

// src/index.ts
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/index.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/hono.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/hono-base.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/compose.js
init_modules_watch_stub();
var compose = /* @__PURE__ */ __name((middleware, onError3, onNotFound) => {
  return (context, next) => {
    let index = -1;
    return dispatch(0);
    async function dispatch(i) {
      if (i <= index) {
        throw new Error("next() called multiple times");
      }
      index = i;
      let res;
      let isError = false;
      let handler;
      if (middleware[i]) {
        handler = middleware[i][0][0];
        context.req.routeIndex = i;
      } else {
        handler = i === middleware.length && next || void 0;
      }
      if (handler) {
        try {
          res = await handler(context, () => dispatch(i + 1));
        } catch (err) {
          if (err instanceof Error && onError3) {
            context.error = err;
            res = await onError3(err, context);
            isError = true;
          } else {
            throw err;
          }
        }
      } else {
        if (context.finalized === false && onNotFound) {
          res = await onNotFound(context);
        }
      }
      if (res && (context.finalized === false || isError)) {
        context.res = res;
      }
      return context;
    }
    __name(dispatch, "dispatch");
  };
}, "compose");

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/context.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/request.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/request/constants.js
init_modules_watch_stub();
var GET_MATCH_RESULT = Symbol();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/body.js
init_modules_watch_stub();
var parseBody = /* @__PURE__ */ __name(async (request, options = /* @__PURE__ */ Object.create(null)) => {
  const { all = false, dot = false } = options;
  const headers = request instanceof HonoRequest ? request.raw.headers : request.headers;
  const contentType = headers.get("Content-Type");
  if (contentType?.startsWith("multipart/form-data") || contentType?.startsWith("application/x-www-form-urlencoded")) {
    return parseFormData(request, { all, dot });
  }
  return {};
}, "parseBody");
async function parseFormData(request, options) {
  const formData = await request.formData();
  if (formData) {
    return convertFormDataToBodyData(formData, options);
  }
  return {};
}
__name(parseFormData, "parseFormData");
function convertFormDataToBodyData(formData, options) {
  const form = /* @__PURE__ */ Object.create(null);
  formData.forEach((value, key) => {
    const shouldParseAllValues = options.all || key.endsWith("[]");
    if (!shouldParseAllValues) {
      form[key] = value;
    } else {
      handleParsingAllValues(form, key, value);
    }
  });
  if (options.dot) {
    Object.entries(form).forEach(([key, value]) => {
      const shouldParseDotValues = key.includes(".");
      if (shouldParseDotValues) {
        handleParsingNestedValues(form, key, value);
        delete form[key];
      }
    });
  }
  return form;
}
__name(convertFormDataToBodyData, "convertFormDataToBodyData");
var handleParsingAllValues = /* @__PURE__ */ __name((form, key, value) => {
  if (form[key] !== void 0) {
    if (Array.isArray(form[key])) {
      ;
      form[key].push(value);
    } else {
      form[key] = [form[key], value];
    }
  } else {
    if (!key.endsWith("[]")) {
      form[key] = value;
    } else {
      form[key] = [value];
    }
  }
}, "handleParsingAllValues");
var handleParsingNestedValues = /* @__PURE__ */ __name((form, key, value) => {
  let nestedForm = form;
  const keys = key.split(".");
  keys.forEach((key2, index) => {
    if (index === keys.length - 1) {
      nestedForm[key2] = value;
    } else {
      if (!nestedForm[key2] || typeof nestedForm[key2] !== "object" || Array.isArray(nestedForm[key2]) || nestedForm[key2] instanceof File) {
        nestedForm[key2] = /* @__PURE__ */ Object.create(null);
      }
      nestedForm = nestedForm[key2];
    }
  });
}, "handleParsingNestedValues");

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/url.js
init_modules_watch_stub();
var splitPath = /* @__PURE__ */ __name((path) => {
  const paths = path.split("/");
  if (paths[0] === "") {
    paths.shift();
  }
  return paths;
}, "splitPath");
var splitRoutingPath = /* @__PURE__ */ __name((routePath) => {
  const { groups, path } = extractGroupsFromPath(routePath);
  const paths = splitPath(path);
  return replaceGroupMarks(paths, groups);
}, "splitRoutingPath");
var extractGroupsFromPath = /* @__PURE__ */ __name((path) => {
  const groups = [];
  path = path.replace(/\{[^}]+\}/g, (match, index) => {
    const mark = `@${index}`;
    groups.push([mark, match]);
    return mark;
  });
  return { groups, path };
}, "extractGroupsFromPath");
var replaceGroupMarks = /* @__PURE__ */ __name((paths, groups) => {
  for (let i = groups.length - 1; i >= 0; i--) {
    const [mark] = groups[i];
    for (let j = paths.length - 1; j >= 0; j--) {
      if (paths[j].includes(mark)) {
        paths[j] = paths[j].replace(mark, groups[i][1]);
        break;
      }
    }
  }
  return paths;
}, "replaceGroupMarks");
var patternCache = {};
var getPattern = /* @__PURE__ */ __name((label, next) => {
  if (label === "*") {
    return "*";
  }
  const match = label.match(/^\:([^\{\}]+)(?:\{(.+)\})?$/);
  if (match) {
    const cacheKey = `${label}#${next}`;
    if (!patternCache[cacheKey]) {
      if (match[2]) {
        patternCache[cacheKey] = next && next[0] !== ":" && next[0] !== "*" ? [cacheKey, match[1], new RegExp(`^${match[2]}(?=/${next})`)] : [label, match[1], new RegExp(`^${match[2]}$`)];
      } else {
        patternCache[cacheKey] = [label, match[1], true];
      }
    }
    return patternCache[cacheKey];
  }
  return null;
}, "getPattern");
var tryDecode = /* @__PURE__ */ __name((str, decoder) => {
  try {
    return decoder(str);
  } catch {
    return str.replace(/(?:%[0-9A-Fa-f]{2})+/g, (match) => {
      try {
        return decoder(match);
      } catch {
        return match;
      }
    });
  }
}, "tryDecode");
var tryDecodeURI = /* @__PURE__ */ __name((str) => tryDecode(str, decodeURI), "tryDecodeURI");
var getPath = /* @__PURE__ */ __name((request) => {
  const url = request.url;
  const start = url.indexOf(
    "/",
    url.charCodeAt(9) === 58 ? 13 : 8
  );
  let i = start;
  for (; i < url.length; i++) {
    const charCode = url.charCodeAt(i);
    if (charCode === 37) {
      const queryIndex = url.indexOf("?", i);
      const path = url.slice(start, queryIndex === -1 ? void 0 : queryIndex);
      return tryDecodeURI(path.includes("%25") ? path.replace(/%25/g, "%2525") : path);
    } else if (charCode === 63) {
      break;
    }
  }
  return url.slice(start, i);
}, "getPath");
var getPathNoStrict = /* @__PURE__ */ __name((request) => {
  const result = getPath(request);
  return result.length > 1 && result.at(-1) === "/" ? result.slice(0, -1) : result;
}, "getPathNoStrict");
var mergePath = /* @__PURE__ */ __name((base, sub, ...rest) => {
  if (rest.length) {
    sub = mergePath(sub, ...rest);
  }
  return `${base?.[0] === "/" ? "" : "/"}${base}${sub === "/" ? "" : `${base?.at(-1) === "/" ? "" : "/"}${sub?.[0] === "/" ? sub.slice(1) : sub}`}`;
}, "mergePath");
var checkOptionalParameter = /* @__PURE__ */ __name((path) => {
  if (path.charCodeAt(path.length - 1) !== 63 || !path.includes(":")) {
    return null;
  }
  const segments = path.split("/");
  const results = [];
  let basePath = "";
  segments.forEach((segment) => {
    if (segment !== "" && !/\:/.test(segment)) {
      basePath += "/" + segment;
    } else if (/\:/.test(segment)) {
      if (/\?/.test(segment)) {
        if (results.length === 0 && basePath === "") {
          results.push("/");
        } else {
          results.push(basePath);
        }
        const optionalSegment = segment.replace("?", "");
        basePath += "/" + optionalSegment;
        results.push(basePath);
      } else {
        basePath += "/" + segment;
      }
    }
  });
  return results.filter((v, i, a) => a.indexOf(v) === i);
}, "checkOptionalParameter");
var _decodeURI = /* @__PURE__ */ __name((value) => {
  if (!/[%+]/.test(value)) {
    return value;
  }
  if (value.indexOf("+") !== -1) {
    value = value.replace(/\+/g, " ");
  }
  return value.indexOf("%") !== -1 ? tryDecode(value, decodeURIComponent_) : value;
}, "_decodeURI");
var _getQueryParam = /* @__PURE__ */ __name((url, key, multiple) => {
  let encoded;
  if (!multiple && key && !/[%+]/.test(key)) {
    let keyIndex2 = url.indexOf(`?${key}`, 8);
    if (keyIndex2 === -1) {
      keyIndex2 = url.indexOf(`&${key}`, 8);
    }
    while (keyIndex2 !== -1) {
      const trailingKeyCode = url.charCodeAt(keyIndex2 + key.length + 1);
      if (trailingKeyCode === 61) {
        const valueIndex = keyIndex2 + key.length + 2;
        const endIndex = url.indexOf("&", valueIndex);
        return _decodeURI(url.slice(valueIndex, endIndex === -1 ? void 0 : endIndex));
      } else if (trailingKeyCode == 38 || isNaN(trailingKeyCode)) {
        return "";
      }
      keyIndex2 = url.indexOf(`&${key}`, keyIndex2 + 1);
    }
    encoded = /[%+]/.test(url);
    if (!encoded) {
      return void 0;
    }
  }
  const results = {};
  encoded ??= /[%+]/.test(url);
  let keyIndex = url.indexOf("?", 8);
  while (keyIndex !== -1) {
    const nextKeyIndex = url.indexOf("&", keyIndex + 1);
    let valueIndex = url.indexOf("=", keyIndex);
    if (valueIndex > nextKeyIndex && nextKeyIndex !== -1) {
      valueIndex = -1;
    }
    let name2 = url.slice(
      keyIndex + 1,
      valueIndex === -1 ? nextKeyIndex === -1 ? void 0 : nextKeyIndex : valueIndex
    );
    if (encoded) {
      name2 = _decodeURI(name2);
    }
    keyIndex = nextKeyIndex;
    if (name2 === "") {
      continue;
    }
    let value;
    if (valueIndex === -1) {
      value = "";
    } else {
      value = url.slice(valueIndex + 1, nextKeyIndex === -1 ? void 0 : nextKeyIndex);
      if (encoded) {
        value = _decodeURI(value);
      }
    }
    if (multiple) {
      if (!(results[name2] && Array.isArray(results[name2]))) {
        results[name2] = [];
      }
      ;
      results[name2].push(value);
    } else {
      results[name2] ??= value;
    }
  }
  return key ? results[key] : results;
}, "_getQueryParam");
var getQueryParam = _getQueryParam;
var getQueryParams = /* @__PURE__ */ __name((url, key) => {
  return _getQueryParam(url, key, true);
}, "getQueryParams");
var decodeURIComponent_ = decodeURIComponent;

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/request.js
var tryDecodeURIComponent = /* @__PURE__ */ __name((str) => tryDecode(str, decodeURIComponent_), "tryDecodeURIComponent");
var HonoRequest = class {
  static {
    __name(this, "HonoRequest");
  }
  raw;
  #validatedData;
  #matchResult;
  routeIndex = 0;
  path;
  bodyCache = {};
  constructor(request, path = "/", matchResult = [[]]) {
    this.raw = request;
    this.path = path;
    this.#matchResult = matchResult;
    this.#validatedData = {};
  }
  param(key) {
    return key ? this.#getDecodedParam(key) : this.#getAllDecodedParams();
  }
  #getDecodedParam(key) {
    const paramKey = this.#matchResult[0][this.routeIndex][1][key];
    const param = this.#getParamValue(paramKey);
    return param ? /\%/.test(param) ? tryDecodeURIComponent(param) : param : void 0;
  }
  #getAllDecodedParams() {
    const decoded = {};
    const keys = Object.keys(this.#matchResult[0][this.routeIndex][1]);
    for (const key of keys) {
      const value = this.#getParamValue(this.#matchResult[0][this.routeIndex][1][key]);
      if (value && typeof value === "string") {
        decoded[key] = /\%/.test(value) ? tryDecodeURIComponent(value) : value;
      }
    }
    return decoded;
  }
  #getParamValue(paramKey) {
    return this.#matchResult[1] ? this.#matchResult[1][paramKey] : paramKey;
  }
  query(key) {
    return getQueryParam(this.url, key);
  }
  queries(key) {
    return getQueryParams(this.url, key);
  }
  header(name2) {
    if (name2) {
      return this.raw.headers.get(name2) ?? void 0;
    }
    const headerData = {};
    this.raw.headers.forEach((value, key) => {
      headerData[key] = value;
    });
    return headerData;
  }
  async parseBody(options) {
    return this.bodyCache.parsedBody ??= await parseBody(this, options);
  }
  #cachedBody = /* @__PURE__ */ __name((key) => {
    const { bodyCache, raw: raw3 } = this;
    const cachedBody = bodyCache[key];
    if (cachedBody) {
      return cachedBody;
    }
    const anyCachedKey = Object.keys(bodyCache)[0];
    if (anyCachedKey) {
      return bodyCache[anyCachedKey].then((body) => {
        if (anyCachedKey === "json") {
          body = JSON.stringify(body);
        }
        return new Response(body)[key]();
      });
    }
    return bodyCache[key] = raw3[key]();
  }, "#cachedBody");
  json() {
    return this.#cachedBody("text").then((text) => JSON.parse(text));
  }
  text() {
    return this.#cachedBody("text");
  }
  arrayBuffer() {
    return this.#cachedBody("arrayBuffer");
  }
  blob() {
    return this.#cachedBody("blob");
  }
  formData() {
    return this.#cachedBody("formData");
  }
  addValidatedData(target, data) {
    this.#validatedData[target] = data;
  }
  valid(target) {
    return this.#validatedData[target];
  }
  get url() {
    return this.raw.url;
  }
  get method() {
    return this.raw.method;
  }
  get [GET_MATCH_RESULT]() {
    return this.#matchResult;
  }
  get matchedRoutes() {
    return this.#matchResult[0].map(([[, route]]) => route);
  }
  get routePath() {
    return this.#matchResult[0].map(([[, route]]) => route)[this.routeIndex].path;
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/html.js
init_modules_watch_stub();
var HtmlEscapedCallbackPhase = {
  Stringify: 1,
  BeforeStream: 2,
  Stream: 3
};
var raw2 = /* @__PURE__ */ __name((value, callbacks) => {
  const escapedString = new String(value);
  escapedString.isEscaped = true;
  escapedString.callbacks = callbacks;
  return escapedString;
}, "raw");
var resolveCallback = /* @__PURE__ */ __name(async (str, phase, preserveCallbacks, context, buffer) => {
  if (typeof str === "object" && !(str instanceof String)) {
    if (!(str instanceof Promise)) {
      str = str.toString();
    }
    if (str instanceof Promise) {
      str = await str;
    }
  }
  const callbacks = str.callbacks;
  if (!callbacks?.length) {
    return Promise.resolve(str);
  }
  if (buffer) {
    buffer[0] += str;
  } else {
    buffer = [str];
  }
  const resStr = Promise.all(callbacks.map((c) => c({ phase, buffer, context }))).then(
    (res) => Promise.all(
      res.filter(Boolean).map((str2) => resolveCallback(str2, phase, false, context, buffer))
    ).then(() => buffer[0])
  );
  if (preserveCallbacks) {
    return raw2(await resStr, callbacks);
  } else {
    return resStr;
  }
}, "resolveCallback");

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/context.js
var TEXT_PLAIN = "text/plain; charset=UTF-8";
var setDefaultContentType = /* @__PURE__ */ __name((contentType, headers) => {
  return {
    "Content-Type": contentType,
    ...headers
  };
}, "setDefaultContentType");
var Context = class {
  static {
    __name(this, "Context");
  }
  #rawRequest;
  #req;
  env = {};
  #var;
  finalized = false;
  error;
  #status;
  #executionCtx;
  #res;
  #layout;
  #renderer;
  #notFoundHandler;
  #preparedHeaders;
  #matchResult;
  #path;
  constructor(req, options) {
    this.#rawRequest = req;
    if (options) {
      this.#executionCtx = options.executionCtx;
      this.env = options.env;
      this.#notFoundHandler = options.notFoundHandler;
      this.#path = options.path;
      this.#matchResult = options.matchResult;
    }
  }
  get req() {
    this.#req ??= new HonoRequest(this.#rawRequest, this.#path, this.#matchResult);
    return this.#req;
  }
  get event() {
    if (this.#executionCtx && "respondWith" in this.#executionCtx) {
      return this.#executionCtx;
    } else {
      throw Error("This context has no FetchEvent");
    }
  }
  get executionCtx() {
    if (this.#executionCtx) {
      return this.#executionCtx;
    } else {
      throw Error("This context has no ExecutionContext");
    }
  }
  get res() {
    return this.#res ||= new Response(null, {
      headers: this.#preparedHeaders ??= new Headers()
    });
  }
  set res(_res) {
    if (this.#res && _res) {
      _res = new Response(_res.body, _res);
      for (const [k, v] of this.#res.headers.entries()) {
        if (k === "content-type") {
          continue;
        }
        if (k === "set-cookie") {
          const cookies = this.#res.headers.getSetCookie();
          _res.headers.delete("set-cookie");
          for (const cookie of cookies) {
            _res.headers.append("set-cookie", cookie);
          }
        } else {
          _res.headers.set(k, v);
        }
      }
    }
    this.#res = _res;
    this.finalized = true;
  }
  render = /* @__PURE__ */ __name((...args) => {
    this.#renderer ??= (content) => this.html(content);
    return this.#renderer(...args);
  }, "render");
  setLayout = /* @__PURE__ */ __name((layout) => this.#layout = layout, "setLayout");
  getLayout = /* @__PURE__ */ __name(() => this.#layout, "getLayout");
  setRenderer = /* @__PURE__ */ __name((renderer) => {
    this.#renderer = renderer;
  }, "setRenderer");
  header = /* @__PURE__ */ __name((name2, value, options) => {
    if (this.finalized) {
      this.#res = new Response(this.#res.body, this.#res);
    }
    const headers = this.#res ? this.#res.headers : this.#preparedHeaders ??= new Headers();
    if (value === void 0) {
      headers.delete(name2);
    } else if (options?.append) {
      headers.append(name2, value);
    } else {
      headers.set(name2, value);
    }
  }, "header");
  status = /* @__PURE__ */ __name((status) => {
    this.#status = status;
  }, "status");
  set = /* @__PURE__ */ __name((key, value) => {
    this.#var ??= /* @__PURE__ */ new Map();
    this.#var.set(key, value);
  }, "set");
  get = /* @__PURE__ */ __name((key) => {
    return this.#var ? this.#var.get(key) : void 0;
  }, "get");
  get var() {
    if (!this.#var) {
      return {};
    }
    return Object.fromEntries(this.#var);
  }
  #newResponse(data, arg, headers) {
    const responseHeaders = this.#res ? new Headers(this.#res.headers) : this.#preparedHeaders ?? new Headers();
    if (typeof arg === "object" && "headers" in arg) {
      const argHeaders = arg.headers instanceof Headers ? arg.headers : new Headers(arg.headers);
      for (const [key, value] of argHeaders) {
        if (key.toLowerCase() === "set-cookie") {
          responseHeaders.append(key, value);
        } else {
          responseHeaders.set(key, value);
        }
      }
    }
    if (headers) {
      for (const [k, v] of Object.entries(headers)) {
        if (typeof v === "string") {
          responseHeaders.set(k, v);
        } else {
          responseHeaders.delete(k);
          for (const v2 of v) {
            responseHeaders.append(k, v2);
          }
        }
      }
    }
    const status = typeof arg === "number" ? arg : arg?.status ?? this.#status;
    return new Response(data, { status, headers: responseHeaders });
  }
  newResponse = /* @__PURE__ */ __name((...args) => this.#newResponse(...args), "newResponse");
  body = /* @__PURE__ */ __name((data, arg, headers) => this.#newResponse(data, arg, headers), "body");
  text = /* @__PURE__ */ __name((text, arg, headers) => {
    return !this.#preparedHeaders && !this.#status && !arg && !headers && !this.finalized ? new Response(text) : this.#newResponse(
      text,
      arg,
      setDefaultContentType(TEXT_PLAIN, headers)
    );
  }, "text");
  json = /* @__PURE__ */ __name((object, arg, headers) => {
    return this.#newResponse(
      JSON.stringify(object),
      arg,
      setDefaultContentType("application/json", headers)
    );
  }, "json");
  html = /* @__PURE__ */ __name((html, arg, headers) => {
    const res = /* @__PURE__ */ __name((html2) => this.#newResponse(html2, arg, setDefaultContentType("text/html; charset=UTF-8", headers)), "res");
    return typeof html === "object" ? resolveCallback(html, HtmlEscapedCallbackPhase.Stringify, false, {}).then(res) : res(html);
  }, "html");
  redirect = /* @__PURE__ */ __name((location, status) => {
    const locationString = String(location);
    this.header(
      "Location",
      !/[^\x00-\xFF]/.test(locationString) ? locationString : encodeURI(locationString)
    );
    return this.newResponse(null, status ?? 302);
  }, "redirect");
  notFound = /* @__PURE__ */ __name(() => {
    this.#notFoundHandler ??= () => new Response();
    return this.#notFoundHandler(this);
  }, "notFound");
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router.js
init_modules_watch_stub();
var METHOD_NAME_ALL = "ALL";
var METHOD_NAME_ALL_LOWERCASE = "all";
var METHODS = ["get", "post", "put", "delete", "options", "patch"];
var MESSAGE_MATCHER_IS_ALREADY_BUILT = "Can not add a route since the matcher is already built.";
var UnsupportedPathError = class extends Error {
  static {
    __name(this, "UnsupportedPathError");
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/utils/constants.js
init_modules_watch_stub();
var COMPOSED_HANDLER = "__COMPOSED_HANDLER";

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/hono-base.js
var notFoundHandler = /* @__PURE__ */ __name((c) => {
  return c.text("404 Not Found", 404);
}, "notFoundHandler");
var errorHandler = /* @__PURE__ */ __name((err, c) => {
  if ("getResponse" in err) {
    const res = err.getResponse();
    return c.newResponse(res.body, res);
  }
  console.error(err);
  return c.text("Internal Server Error", 500);
}, "errorHandler");
var Hono = class {
  static {
    __name(this, "Hono");
  }
  get;
  post;
  put;
  delete;
  options;
  patch;
  all;
  on;
  use;
  router;
  getPath;
  _basePath = "/";
  #path = "/";
  routes = [];
  constructor(options = {}) {
    const allMethods = [...METHODS, METHOD_NAME_ALL_LOWERCASE];
    allMethods.forEach((method) => {
      this[method] = (args1, ...args) => {
        if (typeof args1 === "string") {
          this.#path = args1;
        } else {
          this.#addRoute(method, this.#path, args1);
        }
        args.forEach((handler) => {
          this.#addRoute(method, this.#path, handler);
        });
        return this;
      };
    });
    this.on = (method, path, ...handlers) => {
      for (const p of [path].flat()) {
        this.#path = p;
        for (const m of [method].flat()) {
          handlers.map((handler) => {
            this.#addRoute(m.toUpperCase(), this.#path, handler);
          });
        }
      }
      return this;
    };
    this.use = (arg1, ...handlers) => {
      if (typeof arg1 === "string") {
        this.#path = arg1;
      } else {
        this.#path = "*";
        handlers.unshift(arg1);
      }
      handlers.forEach((handler) => {
        this.#addRoute(METHOD_NAME_ALL, this.#path, handler);
      });
      return this;
    };
    const { strict, ...optionsWithoutStrict } = options;
    Object.assign(this, optionsWithoutStrict);
    this.getPath = strict ?? true ? options.getPath ?? getPath : getPathNoStrict;
  }
  #clone() {
    const clone = new Hono({
      router: this.router,
      getPath: this.getPath
    });
    clone.errorHandler = this.errorHandler;
    clone.#notFoundHandler = this.#notFoundHandler;
    clone.routes = this.routes;
    return clone;
  }
  #notFoundHandler = notFoundHandler;
  errorHandler = errorHandler;
  route(path, app2) {
    const subApp = this.basePath(path);
    app2.routes.map((r) => {
      let handler;
      if (app2.errorHandler === errorHandler) {
        handler = r.handler;
      } else {
        handler = /* @__PURE__ */ __name(async (c, next) => (await compose([], app2.errorHandler)(c, () => r.handler(c, next))).res, "handler");
        handler[COMPOSED_HANDLER] = r.handler;
      }
      subApp.#addRoute(r.method, r.path, handler);
    });
    return this;
  }
  basePath(path) {
    const subApp = this.#clone();
    subApp._basePath = mergePath(this._basePath, path);
    return subApp;
  }
  onError = /* @__PURE__ */ __name((handler) => {
    this.errorHandler = handler;
    return this;
  }, "onError");
  notFound = /* @__PURE__ */ __name((handler) => {
    this.#notFoundHandler = handler;
    return this;
  }, "notFound");
  mount(path, applicationHandler, options) {
    let replaceRequest;
    let optionHandler;
    if (options) {
      if (typeof options === "function") {
        optionHandler = options;
      } else {
        optionHandler = options.optionHandler;
        if (options.replaceRequest === false) {
          replaceRequest = /* @__PURE__ */ __name((request) => request, "replaceRequest");
        } else {
          replaceRequest = options.replaceRequest;
        }
      }
    }
    const getOptions = optionHandler ? (c) => {
      const options2 = optionHandler(c);
      return Array.isArray(options2) ? options2 : [options2];
    } : (c) => {
      let executionContext = void 0;
      try {
        executionContext = c.executionCtx;
      } catch {
      }
      return [c.env, executionContext];
    };
    replaceRequest ||= (() => {
      const mergedPath = mergePath(this._basePath, path);
      const pathPrefixLength = mergedPath === "/" ? 0 : mergedPath.length;
      return (request) => {
        const url = new URL(request.url);
        url.pathname = url.pathname.slice(pathPrefixLength) || "/";
        return new Request(url, request);
      };
    })();
    const handler = /* @__PURE__ */ __name(async (c, next) => {
      const res = await applicationHandler(replaceRequest(c.req.raw), ...getOptions(c));
      if (res) {
        return res;
      }
      await next();
    }, "handler");
    this.#addRoute(METHOD_NAME_ALL, mergePath(path, "*"), handler);
    return this;
  }
  #addRoute(method, path, handler) {
    method = method.toUpperCase();
    path = mergePath(this._basePath, path);
    const r = { basePath: this._basePath, path, method, handler };
    this.router.add(method, path, [handler, r]);
    this.routes.push(r);
  }
  #handleError(err, c) {
    if (err instanceof Error) {
      return this.errorHandler(err, c);
    }
    throw err;
  }
  #dispatch(request, executionCtx, env, method) {
    if (method === "HEAD") {
      return (async () => new Response(null, await this.#dispatch(request, executionCtx, env, "GET")))();
    }
    const path = this.getPath(request, { env });
    const matchResult = this.router.match(method, path);
    const c = new Context(request, {
      path,
      matchResult,
      env,
      executionCtx,
      notFoundHandler: this.#notFoundHandler
    });
    if (matchResult[0].length === 1) {
      let res;
      try {
        res = matchResult[0][0][0][0](c, async () => {
          c.res = await this.#notFoundHandler(c);
        });
      } catch (err) {
        return this.#handleError(err, c);
      }
      return res instanceof Promise ? res.then(
        (resolved) => resolved || (c.finalized ? c.res : this.#notFoundHandler(c))
      ).catch((err) => this.#handleError(err, c)) : res ?? this.#notFoundHandler(c);
    }
    const composed = compose(matchResult[0], this.errorHandler, this.#notFoundHandler);
    return (async () => {
      try {
        const context = await composed(c);
        if (!context.finalized) {
          throw new Error(
            "Context is not finalized. Did you forget to return a Response object or `await next()`?"
          );
        }
        return context.res;
      } catch (err) {
        return this.#handleError(err, c);
      }
    })();
  }
  fetch = /* @__PURE__ */ __name((request, ...rest) => {
    return this.#dispatch(request, rest[1], rest[0], request.method);
  }, "fetch");
  request = /* @__PURE__ */ __name((input, requestInit, Env, executionCtx) => {
    if (input instanceof Request) {
      return this.fetch(requestInit ? new Request(input, requestInit) : input, Env, executionCtx);
    }
    input = input.toString();
    return this.fetch(
      new Request(
        /^https?:\/\//.test(input) ? input : `http://localhost${mergePath("/", input)}`,
        requestInit
      ),
      Env,
      executionCtx
    );
  }, "request");
  fire = /* @__PURE__ */ __name(() => {
    addEventListener("fetch", (event) => {
      event.respondWith(this.#dispatch(event.request, event, void 0, event.request.method));
    });
  }, "fire");
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/reg-exp-router/index.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/reg-exp-router/router.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/reg-exp-router/node.js
init_modules_watch_stub();
var LABEL_REG_EXP_STR = "[^/]+";
var ONLY_WILDCARD_REG_EXP_STR = ".*";
var TAIL_WILDCARD_REG_EXP_STR = "(?:|/.*)";
var PATH_ERROR = Symbol();
var regExpMetaChars = new Set(".\\+*[^]$()");
function compareKey(a, b) {
  if (a.length === 1) {
    return b.length === 1 ? a < b ? -1 : 1 : -1;
  }
  if (b.length === 1) {
    return 1;
  }
  if (a === ONLY_WILDCARD_REG_EXP_STR || a === TAIL_WILDCARD_REG_EXP_STR) {
    return 1;
  } else if (b === ONLY_WILDCARD_REG_EXP_STR || b === TAIL_WILDCARD_REG_EXP_STR) {
    return -1;
  }
  if (a === LABEL_REG_EXP_STR) {
    return 1;
  } else if (b === LABEL_REG_EXP_STR) {
    return -1;
  }
  return a.length === b.length ? a < b ? -1 : 1 : b.length - a.length;
}
__name(compareKey, "compareKey");
var Node = class {
  static {
    __name(this, "Node");
  }
  #index;
  #varIndex;
  #children = /* @__PURE__ */ Object.create(null);
  insert(tokens, index, paramMap, context, pathErrorCheckOnly) {
    if (tokens.length === 0) {
      if (this.#index !== void 0) {
        throw PATH_ERROR;
      }
      if (pathErrorCheckOnly) {
        return;
      }
      this.#index = index;
      return;
    }
    const [token, ...restTokens] = tokens;
    const pattern = token === "*" ? restTokens.length === 0 ? ["", "", ONLY_WILDCARD_REG_EXP_STR] : ["", "", LABEL_REG_EXP_STR] : token === "/*" ? ["", "", TAIL_WILDCARD_REG_EXP_STR] : token.match(/^\:([^\{\}]+)(?:\{(.+)\})?$/);
    let node;
    if (pattern) {
      const name2 = pattern[1];
      let regexpStr = pattern[2] || LABEL_REG_EXP_STR;
      if (name2 && pattern[2]) {
        regexpStr = regexpStr.replace(/^\((?!\?:)(?=[^)]+\)$)/, "(?:");
        if (/\((?!\?:)/.test(regexpStr)) {
          throw PATH_ERROR;
        }
      }
      node = this.#children[regexpStr];
      if (!node) {
        if (Object.keys(this.#children).some(
          (k) => k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR
        )) {
          throw PATH_ERROR;
        }
        if (pathErrorCheckOnly) {
          return;
        }
        node = this.#children[regexpStr] = new Node();
        if (name2 !== "") {
          node.#varIndex = context.varIndex++;
        }
      }
      if (!pathErrorCheckOnly && name2 !== "") {
        paramMap.push([name2, node.#varIndex]);
      }
    } else {
      node = this.#children[token];
      if (!node) {
        if (Object.keys(this.#children).some(
          (k) => k.length > 1 && k !== ONLY_WILDCARD_REG_EXP_STR && k !== TAIL_WILDCARD_REG_EXP_STR
        )) {
          throw PATH_ERROR;
        }
        if (pathErrorCheckOnly) {
          return;
        }
        node = this.#children[token] = new Node();
      }
    }
    node.insert(restTokens, index, paramMap, context, pathErrorCheckOnly);
  }
  buildRegExpStr() {
    const childKeys = Object.keys(this.#children).sort(compareKey);
    const strList = childKeys.map((k) => {
      const c = this.#children[k];
      return (typeof c.#varIndex === "number" ? `(${k})@${c.#varIndex}` : regExpMetaChars.has(k) ? `\\${k}` : k) + c.buildRegExpStr();
    });
    if (typeof this.#index === "number") {
      strList.unshift(`#${this.#index}`);
    }
    if (strList.length === 0) {
      return "";
    }
    if (strList.length === 1) {
      return strList[0];
    }
    return "(?:" + strList.join("|") + ")";
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/reg-exp-router/trie.js
init_modules_watch_stub();
var Trie = class {
  static {
    __name(this, "Trie");
  }
  #context = { varIndex: 0 };
  #root = new Node();
  insert(path, index, pathErrorCheckOnly) {
    const paramAssoc = [];
    const groups = [];
    for (let i = 0; ; ) {
      let replaced = false;
      path = path.replace(/\{[^}]+\}/g, (m) => {
        const mark = `@\\${i}`;
        groups[i] = [mark, m];
        i++;
        replaced = true;
        return mark;
      });
      if (!replaced) {
        break;
      }
    }
    const tokens = path.match(/(?::[^\/]+)|(?:\/\*$)|./g) || [];
    for (let i = groups.length - 1; i >= 0; i--) {
      const [mark] = groups[i];
      for (let j = tokens.length - 1; j >= 0; j--) {
        if (tokens[j].indexOf(mark) !== -1) {
          tokens[j] = tokens[j].replace(mark, groups[i][1]);
          break;
        }
      }
    }
    this.#root.insert(tokens, index, paramAssoc, this.#context, pathErrorCheckOnly);
    return paramAssoc;
  }
  buildRegExp() {
    let regexp = this.#root.buildRegExpStr();
    if (regexp === "") {
      return [/^$/, [], []];
    }
    let captureIndex = 0;
    const indexReplacementMap = [];
    const paramReplacementMap = [];
    regexp = regexp.replace(/#(\d+)|@(\d+)|\.\*\$/g, (_, handlerIndex, paramIndex) => {
      if (handlerIndex !== void 0) {
        indexReplacementMap[++captureIndex] = Number(handlerIndex);
        return "$()";
      }
      if (paramIndex !== void 0) {
        paramReplacementMap[Number(paramIndex)] = ++captureIndex;
        return "";
      }
      return "";
    });
    return [new RegExp(`^${regexp}`), indexReplacementMap, paramReplacementMap];
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/reg-exp-router/router.js
var emptyParam = [];
var nullMatcher = [/^$/, [], /* @__PURE__ */ Object.create(null)];
var wildcardRegExpCache = /* @__PURE__ */ Object.create(null);
function buildWildcardRegExp(path) {
  return wildcardRegExpCache[path] ??= new RegExp(
    path === "*" ? "" : `^${path.replace(
      /\/\*$|([.\\+*[^\]$()])/g,
      (_, metaChar) => metaChar ? `\\${metaChar}` : "(?:|/.*)"
    )}$`
  );
}
__name(buildWildcardRegExp, "buildWildcardRegExp");
function clearWildcardRegExpCache() {
  wildcardRegExpCache = /* @__PURE__ */ Object.create(null);
}
__name(clearWildcardRegExpCache, "clearWildcardRegExpCache");
function buildMatcherFromPreprocessedRoutes(routes) {
  const trie = new Trie();
  const handlerData = [];
  if (routes.length === 0) {
    return nullMatcher;
  }
  const routesWithStaticPathFlag = routes.map(
    (route) => [!/\*|\/:/.test(route[0]), ...route]
  ).sort(
    ([isStaticA, pathA], [isStaticB, pathB]) => isStaticA ? 1 : isStaticB ? -1 : pathA.length - pathB.length
  );
  const staticMap = /* @__PURE__ */ Object.create(null);
  for (let i = 0, j = -1, len = routesWithStaticPathFlag.length; i < len; i++) {
    const [pathErrorCheckOnly, path, handlers] = routesWithStaticPathFlag[i];
    if (pathErrorCheckOnly) {
      staticMap[path] = [handlers.map(([h]) => [h, /* @__PURE__ */ Object.create(null)]), emptyParam];
    } else {
      j++;
    }
    let paramAssoc;
    try {
      paramAssoc = trie.insert(path, j, pathErrorCheckOnly);
    } catch (e) {
      throw e === PATH_ERROR ? new UnsupportedPathError(path) : e;
    }
    if (pathErrorCheckOnly) {
      continue;
    }
    handlerData[j] = handlers.map(([h, paramCount]) => {
      const paramIndexMap = /* @__PURE__ */ Object.create(null);
      paramCount -= 1;
      for (; paramCount >= 0; paramCount--) {
        const [key, value] = paramAssoc[paramCount];
        paramIndexMap[key] = value;
      }
      return [h, paramIndexMap];
    });
  }
  const [regexp, indexReplacementMap, paramReplacementMap] = trie.buildRegExp();
  for (let i = 0, len = handlerData.length; i < len; i++) {
    for (let j = 0, len2 = handlerData[i].length; j < len2; j++) {
      const map = handlerData[i][j]?.[1];
      if (!map) {
        continue;
      }
      const keys = Object.keys(map);
      for (let k = 0, len3 = keys.length; k < len3; k++) {
        map[keys[k]] = paramReplacementMap[map[keys[k]]];
      }
    }
  }
  const handlerMap = [];
  for (const i in indexReplacementMap) {
    handlerMap[i] = handlerData[indexReplacementMap[i]];
  }
  return [regexp, handlerMap, staticMap];
}
__name(buildMatcherFromPreprocessedRoutes, "buildMatcherFromPreprocessedRoutes");
function findMiddleware(middleware, path) {
  if (!middleware) {
    return void 0;
  }
  for (const k of Object.keys(middleware).sort((a, b) => b.length - a.length)) {
    if (buildWildcardRegExp(k).test(path)) {
      return [...middleware[k]];
    }
  }
  return void 0;
}
__name(findMiddleware, "findMiddleware");
var RegExpRouter = class {
  static {
    __name(this, "RegExpRouter");
  }
  name = "RegExpRouter";
  #middleware;
  #routes;
  constructor() {
    this.#middleware = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };
    this.#routes = { [METHOD_NAME_ALL]: /* @__PURE__ */ Object.create(null) };
  }
  add(method, path, handler) {
    const middleware = this.#middleware;
    const routes = this.#routes;
    if (!middleware || !routes) {
      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);
    }
    if (!middleware[method]) {
      ;
      [middleware, routes].forEach((handlerMap) => {
        handlerMap[method] = /* @__PURE__ */ Object.create(null);
        Object.keys(handlerMap[METHOD_NAME_ALL]).forEach((p) => {
          handlerMap[method][p] = [...handlerMap[METHOD_NAME_ALL][p]];
        });
      });
    }
    if (path === "/*") {
      path = "*";
    }
    const paramCount = (path.match(/\/:/g) || []).length;
    if (/\*$/.test(path)) {
      const re = buildWildcardRegExp(path);
      if (method === METHOD_NAME_ALL) {
        Object.keys(middleware).forEach((m) => {
          middleware[m][path] ||= findMiddleware(middleware[m], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];
        });
      } else {
        middleware[method][path] ||= findMiddleware(middleware[method], path) || findMiddleware(middleware[METHOD_NAME_ALL], path) || [];
      }
      Object.keys(middleware).forEach((m) => {
        if (method === METHOD_NAME_ALL || method === m) {
          Object.keys(middleware[m]).forEach((p) => {
            re.test(p) && middleware[m][p].push([handler, paramCount]);
          });
        }
      });
      Object.keys(routes).forEach((m) => {
        if (method === METHOD_NAME_ALL || method === m) {
          Object.keys(routes[m]).forEach(
            (p) => re.test(p) && routes[m][p].push([handler, paramCount])
          );
        }
      });
      return;
    }
    const paths = checkOptionalParameter(path) || [path];
    for (let i = 0, len = paths.length; i < len; i++) {
      const path2 = paths[i];
      Object.keys(routes).forEach((m) => {
        if (method === METHOD_NAME_ALL || method === m) {
          routes[m][path2] ||= [
            ...findMiddleware(middleware[m], path2) || findMiddleware(middleware[METHOD_NAME_ALL], path2) || []
          ];
          routes[m][path2].push([handler, paramCount - len + i + 1]);
        }
      });
    }
  }
  match(method, path) {
    clearWildcardRegExpCache();
    const matchers = this.#buildAllMatchers();
    this.match = (method2, path2) => {
      const matcher = matchers[method2] || matchers[METHOD_NAME_ALL];
      const staticMatch = matcher[2][path2];
      if (staticMatch) {
        return staticMatch;
      }
      const match = path2.match(matcher[0]);
      if (!match) {
        return [[], emptyParam];
      }
      const index = match.indexOf("", 1);
      return [matcher[1][index], match];
    };
    return this.match(method, path);
  }
  #buildAllMatchers() {
    const matchers = /* @__PURE__ */ Object.create(null);
    Object.keys(this.#routes).concat(Object.keys(this.#middleware)).forEach((method) => {
      matchers[method] ||= this.#buildMatcher(method);
    });
    this.#middleware = this.#routes = void 0;
    return matchers;
  }
  #buildMatcher(method) {
    const routes = [];
    let hasOwnRoute = method === METHOD_NAME_ALL;
    [this.#middleware, this.#routes].forEach((r) => {
      const ownRoute = r[method] ? Object.keys(r[method]).map((path) => [path, r[method][path]]) : [];
      if (ownRoute.length !== 0) {
        hasOwnRoute ||= true;
        routes.push(...ownRoute);
      } else if (method !== METHOD_NAME_ALL) {
        routes.push(
          ...Object.keys(r[METHOD_NAME_ALL]).map((path) => [path, r[METHOD_NAME_ALL][path]])
        );
      }
    });
    if (!hasOwnRoute) {
      return null;
    } else {
      return buildMatcherFromPreprocessedRoutes(routes);
    }
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/smart-router/index.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/smart-router/router.js
init_modules_watch_stub();
var SmartRouter = class {
  static {
    __name(this, "SmartRouter");
  }
  name = "SmartRouter";
  #routers = [];
  #routes = [];
  constructor(init3) {
    this.#routers = init3.routers;
  }
  add(method, path, handler) {
    if (!this.#routes) {
      throw new Error(MESSAGE_MATCHER_IS_ALREADY_BUILT);
    }
    this.#routes.push([method, path, handler]);
  }
  match(method, path) {
    if (!this.#routes) {
      throw new Error("Fatal error");
    }
    const routers = this.#routers;
    const routes = this.#routes;
    const len = routers.length;
    let i = 0;
    let res;
    for (; i < len; i++) {
      const router = routers[i];
      try {
        for (let i2 = 0, len2 = routes.length; i2 < len2; i2++) {
          router.add(...routes[i2]);
        }
        res = router.match(method, path);
      } catch (e) {
        if (e instanceof UnsupportedPathError) {
          continue;
        }
        throw e;
      }
      this.match = router.match.bind(router);
      this.#routers = [router];
      this.#routes = void 0;
      break;
    }
    if (i === len) {
      throw new Error("Fatal error");
    }
    this.name = `SmartRouter + ${this.activeRouter.name}`;
    return res;
  }
  get activeRouter() {
    if (this.#routes || this.#routers.length !== 1) {
      throw new Error("No active router has been determined yet.");
    }
    return this.#routers[0];
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/trie-router/index.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/trie-router/router.js
init_modules_watch_stub();

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/trie-router/node.js
init_modules_watch_stub();
var emptyParams = /* @__PURE__ */ Object.create(null);
var Node2 = class {
  static {
    __name(this, "Node");
  }
  #methods;
  #children;
  #patterns;
  #order = 0;
  #params = emptyParams;
  constructor(method, handler, children) {
    this.#children = children || /* @__PURE__ */ Object.create(null);
    this.#methods = [];
    if (method && handler) {
      const m = /* @__PURE__ */ Object.create(null);
      m[method] = { handler, possibleKeys: [], score: 0 };
      this.#methods = [m];
    }
    this.#patterns = [];
  }
  insert(method, path, handler) {
    this.#order = ++this.#order;
    let curNode = this;
    const parts = splitRoutingPath(path);
    const possibleKeys = [];
    for (let i = 0, len = parts.length; i < len; i++) {
      const p = parts[i];
      const nextP = parts[i + 1];
      const pattern = getPattern(p, nextP);
      const key = Array.isArray(pattern) ? pattern[0] : p;
      if (key in curNode.#children) {
        curNode = curNode.#children[key];
        if (pattern) {
          possibleKeys.push(pattern[1]);
        }
        continue;
      }
      curNode.#children[key] = new Node2();
      if (pattern) {
        curNode.#patterns.push(pattern);
        possibleKeys.push(pattern[1]);
      }
      curNode = curNode.#children[key];
    }
    curNode.#methods.push({
      [method]: {
        handler,
        possibleKeys: possibleKeys.filter((v, i, a) => a.indexOf(v) === i),
        score: this.#order
      }
    });
    return curNode;
  }
  #getHandlerSets(node, method, nodeParams, params) {
    const handlerSets = [];
    for (let i = 0, len = node.#methods.length; i < len; i++) {
      const m = node.#methods[i];
      const handlerSet = m[method] || m[METHOD_NAME_ALL];
      const processedSet = {};
      if (handlerSet !== void 0) {
        handlerSet.params = /* @__PURE__ */ Object.create(null);
        handlerSets.push(handlerSet);
        if (nodeParams !== emptyParams || params && params !== emptyParams) {
          for (let i2 = 0, len2 = handlerSet.possibleKeys.length; i2 < len2; i2++) {
            const key = handlerSet.possibleKeys[i2];
            const processed = processedSet[handlerSet.score];
            handlerSet.params[key] = params?.[key] && !processed ? params[key] : nodeParams[key] ?? params?.[key];
            processedSet[handlerSet.score] = true;
          }
        }
      }
    }
    return handlerSets;
  }
  search(method, path) {
    const handlerSets = [];
    this.#params = emptyParams;
    const curNode = this;
    let curNodes = [curNode];
    const parts = splitPath(path);
    const curNodesQueue = [];
    for (let i = 0, len = parts.length; i < len; i++) {
      const part = parts[i];
      const isLast = i === len - 1;
      const tempNodes = [];
      for (let j = 0, len2 = curNodes.length; j < len2; j++) {
        const node = curNodes[j];
        const nextNode = node.#children[part];
        if (nextNode) {
          nextNode.#params = node.#params;
          if (isLast) {
            if (nextNode.#children["*"]) {
              handlerSets.push(
                ...this.#getHandlerSets(nextNode.#children["*"], method, node.#params)
              );
            }
            handlerSets.push(...this.#getHandlerSets(nextNode, method, node.#params));
          } else {
            tempNodes.push(nextNode);
          }
        }
        for (let k = 0, len3 = node.#patterns.length; k < len3; k++) {
          const pattern = node.#patterns[k];
          const params = node.#params === emptyParams ? {} : { ...node.#params };
          if (pattern === "*") {
            const astNode = node.#children["*"];
            if (astNode) {
              handlerSets.push(...this.#getHandlerSets(astNode, method, node.#params));
              astNode.#params = params;
              tempNodes.push(astNode);
            }
            continue;
          }
          if (!part) {
            continue;
          }
          const [key, name2, matcher] = pattern;
          const child = node.#children[key];
          const restPathString = parts.slice(i).join("/");
          if (matcher instanceof RegExp) {
            const m = matcher.exec(restPathString);
            if (m) {
              params[name2] = m[0];
              handlerSets.push(...this.#getHandlerSets(child, method, node.#params, params));
              if (Object.keys(child.#children).length) {
                child.#params = params;
                const componentCount = m[0].match(/\//)?.length ?? 0;
                const targetCurNodes = curNodesQueue[componentCount] ||= [];
                targetCurNodes.push(child);
              }
              continue;
            }
          }
          if (matcher === true || matcher.test(part)) {
            params[name2] = part;
            if (isLast) {
              handlerSets.push(...this.#getHandlerSets(child, method, params, node.#params));
              if (child.#children["*"]) {
                handlerSets.push(
                  ...this.#getHandlerSets(child.#children["*"], method, params, node.#params)
                );
              }
            } else {
              child.#params = params;
              tempNodes.push(child);
            }
          }
        }
      }
      curNodes = tempNodes.concat(curNodesQueue.shift() ?? []);
    }
    if (handlerSets.length > 1) {
      handlerSets.sort((a, b) => {
        return a.score - b.score;
      });
    }
    return [handlerSets.map(({ handler, params }) => [handler, params])];
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/router/trie-router/router.js
var TrieRouter = class {
  static {
    __name(this, "TrieRouter");
  }
  name = "TrieRouter";
  #node;
  constructor() {
    this.#node = new Node2();
  }
  add(method, path, handler) {
    const results = checkOptionalParameter(path);
    if (results) {
      for (let i = 0, len = results.length; i < len; i++) {
        this.#node.insert(method, results[i], handler);
      }
      return;
    }
    this.#node.insert(method, path, handler);
  }
  match(method, path) {
    return this.#node.search(method, path);
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/hono.js
var Hono2 = class extends Hono {
  static {
    __name(this, "Hono");
  }
  constructor(options = {}) {
    super(options);
    this.router = options.router ?? new SmartRouter({
      routers: [new RegExpRouter(), new TrieRouter()]
    });
  }
};

// node_modules/.pnpm/hono@4.8.10/node_modules/hono/dist/middleware/cors/index.js
init_modules_watch_stub();
var cors = /* @__PURE__ */ __name((options) => {
  const defaults = {
    origin: "*",
    allowMethods: ["GET", "HEAD", "PUT", "POST", "DELETE", "PATCH"],
    allowHeaders: [],
    exposeHeaders: []
  };
  const opts = {
    ...defaults,
    ...options
  };
  const findAllowOrigin = ((optsOrigin) => {
    if (typeof optsOrigin === "string") {
      if (optsOrigin === "*") {
        return () => optsOrigin;
      } else {
        return (origin) => optsOrigin === origin ? origin : null;
      }
    } else if (typeof optsOrigin === "function") {
      return optsOrigin;
    } else {
      return (origin) => optsOrigin.includes(origin) ? origin : null;
    }
  })(opts.origin);
  const findAllowMethods = ((optsAllowMethods) => {
    if (typeof optsAllowMethods === "function") {
      return optsAllowMethods;
    } else if (Array.isArray(optsAllowMethods)) {
      return () => optsAllowMethods;
    } else {
      return () => [];
    }
  })(opts.allowMethods);
  return /* @__PURE__ */ __name(async function cors2(c, next) {
    function set(key, value) {
      c.res.headers.set(key, value);
    }
    __name(set, "set");
    const allowOrigin = findAllowOrigin(c.req.header("origin") || "", c);
    if (allowOrigin) {
      set("Access-Control-Allow-Origin", allowOrigin);
    }
    if (opts.origin !== "*") {
      const existingVary = c.req.header("Vary");
      if (existingVary) {
        set("Vary", existingVary);
      } else {
        set("Vary", "Origin");
      }
    }
    if (opts.credentials) {
      set("Access-Control-Allow-Credentials", "true");
    }
    if (opts.exposeHeaders?.length) {
      set("Access-Control-Expose-Headers", opts.exposeHeaders.join(","));
    }
    if (c.req.method === "OPTIONS") {
      if (opts.maxAge != null) {
        set("Access-Control-Max-Age", opts.maxAge.toString());
      }
      const allowMethods = findAllowMethods(c.req.header("origin") || "", c);
      if (allowMethods.length) {
        set("Access-Control-Allow-Methods", allowMethods.join(","));
      }
      let headers = opts.allowHeaders;
      if (!headers?.length) {
        const requestHeaders = c.req.header("Access-Control-Request-Headers");
        if (requestHeaders) {
          headers = requestHeaders.split(/\s*,\s*/);
        }
      }
      if (headers?.length) {
        set("Access-Control-Allow-Headers", headers.join(","));
        c.res.headers.append("Vary", "Access-Control-Request-Headers");
      }
      c.res.headers.delete("Content-Length");
      c.res.headers.delete("Content-Type");
      return new Response(null, {
        headers: c.res.headers,
        status: 204,
        statusText: "No Content"
      });
    }
    await next();
  }, "cors2");
}, "cors");

// src/routes/license.ts
init_modules_watch_stub();

// src/services/license.service.ts
init_modules_watch_stub();
var import_prisma = __toESM(require_default());

// node_modules/.pnpm/@prisma+adapter-d1@6.13.0/node_modules/@prisma/adapter-d1/dist/index.mjs
init_modules_watch_stub();

// node_modules/.pnpm/@prisma+driver-adapter-utils@6.13.0/node_modules/@prisma/driver-adapter-utils/dist/index.mjs
init_modules_watch_stub();

// node_modules/.pnpm/@prisma+debug@6.13.0/node_modules/@prisma/debug/dist/index.mjs
init_modules_watch_stub();
var __defProp2 = Object.defineProperty;
var __export2 = /* @__PURE__ */ __name((target, all) => {
  for (var name2 in all)
    __defProp2(target, name2, { get: all[name2], enumerable: true });
}, "__export");
var colors_exports = {};
__export2(colors_exports, {
  $: /* @__PURE__ */ __name(() => $, "$"),
  bgBlack: /* @__PURE__ */ __name(() => bgBlack, "bgBlack"),
  bgBlue: /* @__PURE__ */ __name(() => bgBlue, "bgBlue"),
  bgCyan: /* @__PURE__ */ __name(() => bgCyan, "bgCyan"),
  bgGreen: /* @__PURE__ */ __name(() => bgGreen, "bgGreen"),
  bgMagenta: /* @__PURE__ */ __name(() => bgMagenta, "bgMagenta"),
  bgRed: /* @__PURE__ */ __name(() => bgRed, "bgRed"),
  bgWhite: /* @__PURE__ */ __name(() => bgWhite, "bgWhite"),
  bgYellow: /* @__PURE__ */ __name(() => bgYellow, "bgYellow"),
  black: /* @__PURE__ */ __name(() => black, "black"),
  blue: /* @__PURE__ */ __name(() => blue, "blue"),
  bold: /* @__PURE__ */ __name(() => bold, "bold"),
  cyan: /* @__PURE__ */ __name(() => cyan, "cyan"),
  dim: /* @__PURE__ */ __name(() => dim, "dim"),
  gray: /* @__PURE__ */ __name(() => gray, "gray"),
  green: /* @__PURE__ */ __name(() => green, "green"),
  grey: /* @__PURE__ */ __name(() => grey, "grey"),
  hidden: /* @__PURE__ */ __name(() => hidden, "hidden"),
  inverse: /* @__PURE__ */ __name(() => inverse, "inverse"),
  italic: /* @__PURE__ */ __name(() => italic, "italic"),
  magenta: /* @__PURE__ */ __name(() => magenta, "magenta"),
  red: /* @__PURE__ */ __name(() => red, "red"),
  reset: /* @__PURE__ */ __name(() => reset, "reset"),
  strikethrough: /* @__PURE__ */ __name(() => strikethrough, "strikethrough"),
  underline: /* @__PURE__ */ __name(() => underline, "underline"),
  white: /* @__PURE__ */ __name(() => white, "white"),
  yellow: /* @__PURE__ */ __name(() => yellow, "yellow")
});
var FORCE_COLOR;
var NODE_DISABLE_COLORS;
var NO_COLOR;
var TERM;
var isTTY = true;
if (typeof process !== "undefined") {
  ({ FORCE_COLOR, NODE_DISABLE_COLORS, NO_COLOR, TERM } = process.env || {});
  isTTY = process.stdout && process.stdout.isTTY;
}
var $ = {
  enabled: !NODE_DISABLE_COLORS && NO_COLOR == null && TERM !== "dumb" && (FORCE_COLOR != null && FORCE_COLOR !== "0" || isTTY)
};
function init(x, y) {
  let rgx = new RegExp(`\\x1b\\[${y}m`, "g");
  let open = `\x1B[${x}m`, close = `\x1B[${y}m`;
  return function(txt) {
    if (!$.enabled || txt == null) return txt;
    return open + (!!~("" + txt).indexOf(close) ? txt.replace(rgx, close + open) : txt) + close;
  };
}
__name(init, "init");
var reset = init(0, 0);
var bold = init(1, 22);
var dim = init(2, 22);
var italic = init(3, 23);
var underline = init(4, 24);
var inverse = init(7, 27);
var hidden = init(8, 28);
var strikethrough = init(9, 29);
var black = init(30, 39);
var red = init(31, 39);
var green = init(32, 39);
var yellow = init(33, 39);
var blue = init(34, 39);
var magenta = init(35, 39);
var cyan = init(36, 39);
var white = init(37, 39);
var gray = init(90, 39);
var grey = init(90, 39);
var bgBlack = init(40, 49);
var bgRed = init(41, 49);
var bgGreen = init(42, 49);
var bgYellow = init(43, 49);
var bgBlue = init(44, 49);
var bgMagenta = init(45, 49);
var bgCyan = init(46, 49);
var bgWhite = init(47, 49);
var MAX_ARGS_HISTORY = 100;
var COLORS = ["green", "yellow", "blue", "magenta", "cyan", "red"];
var argsHistory = [];
var lastTimestamp = Date.now();
var lastColor = 0;
var processEnv = typeof process !== "undefined" ? process.env : {};
globalThis.DEBUG ??= processEnv.DEBUG ?? "";
globalThis.DEBUG_COLORS ??= processEnv.DEBUG_COLORS ? processEnv.DEBUG_COLORS === "true" : true;
var topProps = {
  enable(namespace) {
    if (typeof namespace === "string") {
      globalThis.DEBUG = namespace;
    }
  },
  disable() {
    const prev = globalThis.DEBUG;
    globalThis.DEBUG = "";
    return prev;
  },
  // this is the core logic to check if logging should happen or not
  enabled(namespace) {
    const listenedNamespaces = globalThis.DEBUG.split(",").map((s) => {
      return s.replace(/[.+?^${}()|[\]\\]/g, "\\$&");
    });
    const isListened = listenedNamespaces.some((listenedNamespace) => {
      if (listenedNamespace === "" || listenedNamespace[0] === "-") return false;
      return namespace.match(RegExp(listenedNamespace.split("*").join(".*") + "$"));
    });
    const isExcluded = listenedNamespaces.some((listenedNamespace) => {
      if (listenedNamespace === "" || listenedNamespace[0] !== "-") return false;
      return namespace.match(RegExp(listenedNamespace.slice(1).split("*").join(".*") + "$"));
    });
    return isListened && !isExcluded;
  },
  log: /* @__PURE__ */ __name((...args) => {
    const [namespace, format, ...rest] = args;
    const logWithFormatting = console.warn ?? console.log;
    logWithFormatting(`${namespace} ${format}`, ...rest);
  }, "log"),
  formatters: {}
  // not implemented
};
function debugCreate(namespace) {
  const instanceProps = {
    color: COLORS[lastColor++ % COLORS.length],
    enabled: topProps.enabled(namespace),
    namespace,
    log: topProps.log,
    extend: /* @__PURE__ */ __name(() => {
    }, "extend")
    // not implemented
  };
  const debugCall = /* @__PURE__ */ __name((...args) => {
    const { enabled, namespace: namespace2, color, log } = instanceProps;
    if (args.length !== 0) {
      argsHistory.push([namespace2, ...args]);
    }
    if (argsHistory.length > MAX_ARGS_HISTORY) {
      argsHistory.shift();
    }
    if (topProps.enabled(namespace2) || enabled) {
      const stringArgs = args.map((arg) => {
        if (typeof arg === "string") {
          return arg;
        }
        return safeStringify(arg);
      });
      const ms = `+${Date.now() - lastTimestamp}ms`;
      lastTimestamp = Date.now();
      if (globalThis.DEBUG_COLORS) {
        log(colors_exports[color](bold(namespace2)), ...stringArgs, colors_exports[color](ms));
      } else {
        log(namespace2, ...stringArgs, ms);
      }
    }
  }, "debugCall");
  return new Proxy(debugCall, {
    get: /* @__PURE__ */ __name((_, prop) => instanceProps[prop], "get"),
    set: /* @__PURE__ */ __name((_, prop, value) => instanceProps[prop] = value, "set")
  });
}
__name(debugCreate, "debugCreate");
var Debug2 = new Proxy(debugCreate, {
  get: /* @__PURE__ */ __name((_, prop) => topProps[prop], "get"),
  set: /* @__PURE__ */ __name((_, prop, value) => topProps[prop] = value, "set")
});
function safeStringify(value, indent = 2) {
  const cache = /* @__PURE__ */ new Set();
  return JSON.stringify(
    value,
    (key, value2) => {
      if (typeof value2 === "object" && value2 !== null) {
        if (cache.has(value2)) {
          return `[Circular *]`;
        }
        cache.add(value2);
      } else if (typeof value2 === "bigint") {
        return value2.toString();
      }
      return value2;
    },
    indent
  );
}
__name(safeStringify, "safeStringify");

// node_modules/.pnpm/@prisma+driver-adapter-utils@6.13.0/node_modules/@prisma/driver-adapter-utils/dist/index.mjs
var DriverAdapterError = class extends Error {
  static {
    __name(this, "DriverAdapterError");
  }
  name = "DriverAdapterError";
  cause;
  constructor(payload) {
    super(typeof payload["message"] === "string" ? payload["message"] : payload.kind);
    this.cause = payload;
  }
};
var debug = Debug2("driver-adapter-utils");
var ColumnTypeEnum = {
  // Scalars
  Int32: 0,
  Int64: 1,
  Float: 2,
  Double: 3,
  Numeric: 4,
  Boolean: 5,
  Character: 6,
  Text: 7,
  Date: 8,
  Time: 9,
  DateTime: 10,
  Json: 11,
  Enum: 12,
  Bytes: 13,
  Set: 14,
  Uuid: 15,
  // Arrays
  Int32Array: 64,
  Int64Array: 65,
  FloatArray: 66,
  DoubleArray: 67,
  NumericArray: 68,
  BooleanArray: 69,
  CharacterArray: 70,
  TextArray: 71,
  DateArray: 72,
  TimeArray: 73,
  DateTimeArray: 74,
  JsonArray: 75,
  EnumArray: 76,
  BytesArray: 77,
  UuidArray: 78,
  // Custom
  UnknownNumber: 128
};
var mockAdapterErrors = {
  queryRaw: new Error("Not implemented: queryRaw"),
  executeRaw: new Error("Not implemented: executeRaw"),
  startTransaction: new Error("Not implemented: startTransaction"),
  executeScript: new Error("Not implemented: executeScript"),
  dispose: new Error("Not implemented: dispose")
};

// node_modules/.pnpm/ky@1.7.5/node_modules/ky/distribution/index.js
init_modules_watch_stub();

// node_modules/.pnpm/ky@1.7.5/node_modules/ky/distribution/core/Ky.js
init_modules_watch_stub();

// node_modules/.pnpm/ky@1.7.5/node_modules/ky/distribution/errors/HTTPError.js
init_modules_watch_stub();
var HTTPError = class extends Error {
  static {
    __name(this, "HTTPError");
  }
  response;
  request;
  options;
  constructor(response, request, options) {
    const code = response.status || response.status === 0 ? response.status : "";
    const title = response.statusText || "";
    const status = `${code} ${title}`.trim();
    const reason = status ? `status code ${status}` : "an unknown error";
    super(`Request failed with ${reason}: ${request.method} ${request.url}`);
    this.name = "HTTPError";
    this.response = response;
    this.request = request;
    this.options = options;
  }
};

// node_modules/.pnpm/ky@1.7.5/node_modules/ky/distribution/errors/TimeoutError.js
init_modules_watch_stub();
var TimeoutError = class extends Error {
  static {
    __name(this, "TimeoutError");
  }
  request;
  constructor(request) {
    super(`Request timed out: ${request.method} ${request.url}`);
    this.name = "TimeoutError";
    this.request = request;
  }
};

// node_modules/.pnpm/ky@1.7.5/node_modules/ky/distribution/utils/merge.js
init_modules_watch_stub();

// node_modules/.pnpm/ky@1.7.5/node_modules/ky/distribution/utils/is.js
init_modules_watch_stub();
var isObject = /* @__PURE__ */ __name((value) => value !== null && typeof value === "object", "isObject");

// node_modules/.pnpm/ky@1.7.5/node_modules/ky/distribution/utils/merge.js
var validateAndMerge = /* @__PURE__ */ __name((...sources) => {
  for (const source of sources) {
    if ((!isObject(source) || Array.isArray(source)) && source !== void 0) {
      throw new TypeError("The `options` argument must be an object");
    }
  }
  return deepMerge({}, ...sources);
}, "validateAndMerge");
var mergeHeaders = /* @__PURE__ */ __name((source1 = {}, source2 = {}) => {
  const result = new globalThis.Headers(source1);
  const isHeadersInstance = source2 instanceof globalThis.Headers;
  const source = new globalThis.Headers(source2);
  for (const [key, value] of source.entries()) {
    if (isHeadersInstance && value === "undefined" || value === void 0) {
      result.delete(key);
    } else {
      result.set(key, value);
    }
  }
  return result;
}, "mergeHeaders");
function newHookValue(original, incoming, property) {
  return Object.hasOwn(incoming, property) && incoming[property] === void 0 ? [] : deepMerge(original[property] ?? [], incoming[property] ?? []);
}
__name(newHookValue, "newHookValue");
var mergeHooks = /* @__PURE__ */ __name((original = {}, incoming = {}) => ({
  beforeRequest: newHookValue(original, incoming, "beforeRequest"),
  beforeRetry: newHookValue(original, incoming, "beforeRetry"),
  afterResponse: newHookValue(original, incoming, "afterResponse"),
  beforeError: newHookValue(original, incoming, "beforeError")
}), "mergeHooks");
var deepMerge = /* @__PURE__ */ __name((...sources) => {
  let returnValue = {};
  let headers = {};
  let hooks = {};
  for (const source of sources) {
    if (Array.isArray(source)) {
      if (!Array.isArray(returnValue)) {
        returnValue = [];
      }
      returnValue = [...returnValue, ...source];
    } else if (isObject(source)) {
      for (let [key, value] of Object.entries(source)) {
        if (isObject(value) && key in returnValue) {
          value = deepMerge(returnValue[key], value);
        }
        returnValue = { ...returnValue, [key]: value };
      }
      if (isObject(source.hooks)) {
        hooks = mergeHooks(hooks, source.hooks);
        returnValue.hooks = hooks;
      }
      if (isObject(source.headers)) {
        headers = mergeHeaders(headers, source.headers);
        returnValue.headers = headers;
      }
    }
  }
  return returnValue;
}, "deepMerge");

// node_modules/.pnpm/ky@1.7.5/node_modules/ky/distribution/utils/normalize.js
init_modules_watch_stub();

// node_modules/.pnpm/ky@1.7.5/node_modules/ky/distribution/core/constants.js
init_modules_watch_stub();
var supportsRequestStreams = (() => {
  let duplexAccessed = false;
  let hasContentType = false;
  const supportsReadableStream = typeof globalThis.ReadableStream === "function";
  const supportsRequest = typeof globalThis.Request === "function";
  if (supportsReadableStream && supportsRequest) {
    try {
      hasContentType = new globalThis.Request("https://empty.invalid", {
        body: new globalThis.ReadableStream(),
        method: "POST",
        // @ts-expect-error - Types are outdated.
        get duplex() {
          duplexAccessed = true;
          return "half";
        }
      }).headers.has("Content-Type");
    } catch (error) {
      if (error instanceof Error && error.message === "unsupported BodyInit type") {
        return false;
      }
      throw error;
    }
  }
  return duplexAccessed && !hasContentType;
})();
var supportsAbortController = typeof globalThis.AbortController === "function";
var supportsResponseStreams = typeof globalThis.ReadableStream === "function";
var supportsFormData = typeof globalThis.FormData === "function";
var requestMethods = ["get", "post", "put", "patch", "head", "delete"];
var validate = /* @__PURE__ */ __name(() => void 0, "validate");
validate();
var responseTypes = {
  json: "application/json",
  text: "text/*",
  formData: "multipart/form-data",
  arrayBuffer: "*/*",
  blob: "*/*"
};
var maxSafeTimeout = 2147483647;
var stop = Symbol("stop");
var kyOptionKeys = {
  json: true,
  parseJson: true,
  stringifyJson: true,
  searchParams: true,
  prefixUrl: true,
  retry: true,
  timeout: true,
  hooks: true,
  throwHttpErrors: true,
  onDownloadProgress: true,
  fetch: true
};
var requestOptionsRegistry = {
  method: true,
  headers: true,
  body: true,
  mode: true,
  credentials: true,
  cache: true,
  redirect: true,
  referrer: true,
  referrerPolicy: true,
  integrity: true,
  keepalive: true,
  signal: true,
  window: true,
  dispatcher: true,
  duplex: true,
  priority: true
};

// node_modules/.pnpm/ky@1.7.5/node_modules/ky/distribution/utils/normalize.js
var normalizeRequestMethod = /* @__PURE__ */ __name((input) => requestMethods.includes(input) ? input.toUpperCase() : input, "normalizeRequestMethod");
var retryMethods = ["get", "put", "head", "delete", "options", "trace"];
var retryStatusCodes = [408, 413, 429, 500, 502, 503, 504];
var retryAfterStatusCodes = [413, 429, 503];
var defaultRetryOptions = {
  limit: 2,
  methods: retryMethods,
  statusCodes: retryStatusCodes,
  afterStatusCodes: retryAfterStatusCodes,
  maxRetryAfter: Number.POSITIVE_INFINITY,
  backoffLimit: Number.POSITIVE_INFINITY,
  delay: /* @__PURE__ */ __name((attemptCount) => 0.3 * 2 ** (attemptCount - 1) * 1e3, "delay")
};
var normalizeRetryOptions = /* @__PURE__ */ __name((retry = {}) => {
  if (typeof retry === "number") {
    return {
      ...defaultRetryOptions,
      limit: retry
    };
  }
  if (retry.methods && !Array.isArray(retry.methods)) {
    throw new Error("retry.methods must be an array");
  }
  if (retry.statusCodes && !Array.isArray(retry.statusCodes)) {
    throw new Error("retry.statusCodes must be an array");
  }
  return {
    ...defaultRetryOptions,
    ...retry
  };
}, "normalizeRetryOptions");

// node_modules/.pnpm/ky@1.7.5/node_modules/ky/distribution/utils/timeout.js
init_modules_watch_stub();
async function timeout(request, init3, abortController, options) {
  return new Promise((resolve, reject) => {
    const timeoutId = setTimeout(() => {
      if (abortController) {
        abortController.abort();
      }
      reject(new TimeoutError(request));
    }, options.timeout);
    void options.fetch(request, init3).then(resolve).catch(reject).then(() => {
      clearTimeout(timeoutId);
    });
  });
}
__name(timeout, "timeout");

// node_modules/.pnpm/ky@1.7.5/node_modules/ky/distribution/utils/delay.js
init_modules_watch_stub();
async function delay(ms, { signal }) {
  return new Promise((resolve, reject) => {
    if (signal) {
      signal.throwIfAborted();
      signal.addEventListener("abort", abortHandler, { once: true });
    }
    function abortHandler() {
      clearTimeout(timeoutId);
      reject(signal.reason);
    }
    __name(abortHandler, "abortHandler");
    const timeoutId = setTimeout(() => {
      signal?.removeEventListener("abort", abortHandler);
      resolve();
    }, ms);
  });
}
__name(delay, "delay");

// node_modules/.pnpm/ky@1.7.5/node_modules/ky/distribution/utils/options.js
init_modules_watch_stub();
var findUnknownOptions = /* @__PURE__ */ __name((request, options) => {
  const unknownOptions = {};
  for (const key in options) {
    if (!(key in requestOptionsRegistry) && !(key in kyOptionKeys) && !(key in request)) {
      unknownOptions[key] = options[key];
    }
  }
  return unknownOptions;
}, "findUnknownOptions");

// node_modules/.pnpm/ky@1.7.5/node_modules/ky/distribution/core/Ky.js
var Ky = class _Ky {
  static {
    __name(this, "Ky");
  }
  static create(input, options) {
    const ky2 = new _Ky(input, options);
    const function_ = /* @__PURE__ */ __name(async () => {
      if (typeof ky2._options.timeout === "number" && ky2._options.timeout > maxSafeTimeout) {
        throw new RangeError(`The \`timeout\` option cannot be greater than ${maxSafeTimeout}`);
      }
      await Promise.resolve();
      let response = await ky2._fetch();
      for (const hook of ky2._options.hooks.afterResponse) {
        const modifiedResponse = await hook(ky2.request, ky2._options, ky2._decorateResponse(response.clone()));
        if (modifiedResponse instanceof globalThis.Response) {
          response = modifiedResponse;
        }
      }
      ky2._decorateResponse(response);
      if (!response.ok && ky2._options.throwHttpErrors) {
        let error = new HTTPError(response, ky2.request, ky2._options);
        for (const hook of ky2._options.hooks.beforeError) {
          error = await hook(error);
        }
        throw error;
      }
      if (ky2._options.onDownloadProgress) {
        if (typeof ky2._options.onDownloadProgress !== "function") {
          throw new TypeError("The `onDownloadProgress` option must be a function");
        }
        if (!supportsResponseStreams) {
          throw new Error("Streams are not supported in your environment. `ReadableStream` is missing.");
        }
        return ky2._stream(response.clone(), ky2._options.onDownloadProgress);
      }
      return response;
    }, "function_");
    const isRetriableMethod = ky2._options.retry.methods.includes(ky2.request.method.toLowerCase());
    const result = isRetriableMethod ? ky2._retry(function_) : function_();
    for (const [type, mimeType] of Object.entries(responseTypes)) {
      result[type] = async () => {
        ky2.request.headers.set("accept", ky2.request.headers.get("accept") || mimeType);
        const response = await result;
        if (type === "json") {
          if (response.status === 204) {
            return "";
          }
          const arrayBuffer = await response.clone().arrayBuffer();
          const responseSize = arrayBuffer.byteLength;
          if (responseSize === 0) {
            return "";
          }
          if (options.parseJson) {
            return options.parseJson(await response.text());
          }
        }
        return response[type]();
      };
    }
    return result;
  }
  request;
  abortController;
  _retryCount = 0;
  _input;
  _options;
  // eslint-disable-next-line complexity
  constructor(input, options = {}) {
    this._input = input;
    this._options = {
      ...options,
      headers: mergeHeaders(this._input.headers, options.headers),
      hooks: mergeHooks({
        beforeRequest: [],
        beforeRetry: [],
        beforeError: [],
        afterResponse: []
      }, options.hooks),
      method: normalizeRequestMethod(options.method ?? this._input.method ?? "GET"),
      // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
      prefixUrl: String(options.prefixUrl || ""),
      retry: normalizeRetryOptions(options.retry),
      throwHttpErrors: options.throwHttpErrors !== false,
      timeout: options.timeout ?? 1e4,
      fetch: options.fetch ?? globalThis.fetch.bind(globalThis)
    };
    if (typeof this._input !== "string" && !(this._input instanceof URL || this._input instanceof globalThis.Request)) {
      throw new TypeError("`input` must be a string, URL, or Request");
    }
    if (this._options.prefixUrl && typeof this._input === "string") {
      if (this._input.startsWith("/")) {
        throw new Error("`input` must not begin with a slash when using `prefixUrl`");
      }
      if (!this._options.prefixUrl.endsWith("/")) {
        this._options.prefixUrl += "/";
      }
      this._input = this._options.prefixUrl + this._input;
    }
    if (supportsAbortController) {
      this.abortController = new globalThis.AbortController();
      const originalSignal = this._options.signal ?? this._input.signal;
      if (originalSignal?.aborted) {
        this.abortController.abort(originalSignal?.reason);
      }
      originalSignal?.addEventListener("abort", () => {
        this.abortController.abort(originalSignal.reason);
      });
      this._options.signal = this.abortController.signal;
    }
    if (supportsRequestStreams) {
      this._options.duplex = "half";
    }
    if (this._options.json !== void 0) {
      this._options.body = this._options.stringifyJson?.(this._options.json) ?? JSON.stringify(this._options.json);
      this._options.headers.set("content-type", this._options.headers.get("content-type") ?? "application/json");
    }
    this.request = new globalThis.Request(this._input, this._options);
    if (this._options.searchParams) {
      const textSearchParams = typeof this._options.searchParams === "string" ? this._options.searchParams.replace(/^\?/, "") : new URLSearchParams(this._options.searchParams).toString();
      const searchParams = "?" + textSearchParams;
      const url = this.request.url.replace(/(?:\?.*?)?(?=#|$)/, searchParams);
      if ((supportsFormData && this._options.body instanceof globalThis.FormData || this._options.body instanceof URLSearchParams) && !(this._options.headers && this._options.headers["content-type"])) {
        this.request.headers.delete("content-type");
      }
      this.request = new globalThis.Request(new globalThis.Request(url, { ...this.request }), this._options);
    }
  }
  _calculateRetryDelay(error) {
    this._retryCount++;
    if (this._retryCount > this._options.retry.limit || error instanceof TimeoutError) {
      throw error;
    }
    if (error instanceof HTTPError) {
      if (!this._options.retry.statusCodes.includes(error.response.status)) {
        throw error;
      }
      const retryAfter = error.response.headers.get("Retry-After") ?? error.response.headers.get("RateLimit-Reset") ?? error.response.headers.get("X-RateLimit-Reset") ?? error.response.headers.get("X-Rate-Limit-Reset");
      if (retryAfter && this._options.retry.afterStatusCodes.includes(error.response.status)) {
        let after = Number(retryAfter) * 1e3;
        if (Number.isNaN(after)) {
          after = Date.parse(retryAfter) - Date.now();
        } else if (after >= Date.parse("2024-01-01")) {
          after -= Date.now();
        }
        const max = this._options.retry.maxRetryAfter ?? after;
        return after < max ? after : max;
      }
      if (error.response.status === 413) {
        throw error;
      }
    }
    const retryDelay = this._options.retry.delay(this._retryCount);
    return Math.min(this._options.retry.backoffLimit, retryDelay);
  }
  _decorateResponse(response) {
    if (this._options.parseJson) {
      response.json = async () => this._options.parseJson(await response.text());
    }
    return response;
  }
  async _retry(function_) {
    try {
      return await function_();
    } catch (error) {
      const ms = Math.min(this._calculateRetryDelay(error), maxSafeTimeout);
      if (this._retryCount < 1) {
        throw error;
      }
      await delay(ms, { signal: this._options.signal });
      for (const hook of this._options.hooks.beforeRetry) {
        const hookResult = await hook({
          request: this.request,
          options: this._options,
          error,
          retryCount: this._retryCount
        });
        if (hookResult === stop) {
          return;
        }
      }
      return this._retry(function_);
    }
  }
  async _fetch() {
    for (const hook of this._options.hooks.beforeRequest) {
      const result = await hook(this.request, this._options);
      if (result instanceof Request) {
        this.request = result;
        break;
      }
      if (result instanceof Response) {
        return result;
      }
    }
    const nonRequestOptions = findUnknownOptions(this.request, this._options);
    const mainRequest = this.request;
    this.request = mainRequest.clone();
    if (this._options.timeout === false) {
      return this._options.fetch(mainRequest, nonRequestOptions);
    }
    return timeout(mainRequest, nonRequestOptions, this.abortController, this._options);
  }
  /* istanbul ignore next */
  _stream(response, onDownloadProgress) {
    const totalBytes = Number(response.headers.get("content-length")) || 0;
    let transferredBytes = 0;
    if (response.status === 204) {
      if (onDownloadProgress) {
        onDownloadProgress({ percent: 1, totalBytes, transferredBytes }, new Uint8Array());
      }
      return new globalThis.Response(null, {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers
      });
    }
    return new globalThis.Response(new globalThis.ReadableStream({
      async start(controller) {
        const reader = response.body.getReader();
        if (onDownloadProgress) {
          onDownloadProgress({ percent: 0, transferredBytes: 0, totalBytes }, new Uint8Array());
        }
        async function read() {
          const { done, value } = await reader.read();
          if (done) {
            controller.close();
            return;
          }
          if (onDownloadProgress) {
            transferredBytes += value.byteLength;
            const percent = totalBytes === 0 ? 0 : transferredBytes / totalBytes;
            onDownloadProgress({ percent, transferredBytes, totalBytes }, value);
          }
          controller.enqueue(value);
          await read();
        }
        __name(read, "read");
        await read();
      }
    }), {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers
    });
  }
};

// node_modules/.pnpm/ky@1.7.5/node_modules/ky/distribution/index.js
var createInstance = /* @__PURE__ */ __name((defaults) => {
  const ky2 = /* @__PURE__ */ __name((input, options) => Ky.create(input, validateAndMerge(defaults, options)), "ky");
  for (const method of requestMethods) {
    ky2[method] = (input, options) => Ky.create(input, validateAndMerge(defaults, options, { method }));
  }
  ky2.create = (newDefaults) => createInstance(validateAndMerge(newDefaults));
  ky2.extend = (newDefaults) => {
    if (typeof newDefaults === "function") {
      newDefaults = newDefaults(defaults ?? {});
    }
    return createInstance(validateAndMerge(defaults, newDefaults));
  };
  ky2.stop = stop;
  return ky2;
}, "createInstance");
var ky = createInstance();
var distribution_default = ky;

// node_modules/.pnpm/@prisma+adapter-d1@6.13.0/node_modules/@prisma/adapter-d1/dist/index.mjs
var name = "@prisma/adapter-d1";
var FORCE_COLOR2;
var NODE_DISABLE_COLORS2;
var NO_COLOR2;
var TERM2;
var isTTY2 = true;
if (typeof process !== "undefined") {
  ({ FORCE_COLOR: FORCE_COLOR2, NODE_DISABLE_COLORS: NODE_DISABLE_COLORS2, NO_COLOR: NO_COLOR2, TERM: TERM2 } = process.env || {});
  isTTY2 = process.stdout && process.stdout.isTTY;
}
var $2 = {
  enabled: !NODE_DISABLE_COLORS2 && NO_COLOR2 == null && TERM2 !== "dumb" && (FORCE_COLOR2 != null && FORCE_COLOR2 !== "0" || isTTY2)
};
function init2(x, y) {
  let rgx = new RegExp(`\\x1b\\[${y}m`, "g");
  let open = `\x1B[${x}m`, close = `\x1B[${y}m`;
  return function(txt) {
    if (!$2.enabled || txt == null) return txt;
    return open + (!!~("" + txt).indexOf(close) ? txt.replace(rgx, close + open) : txt) + close;
  };
}
__name(init2, "init");
var reset2 = init2(0, 0);
var bold2 = init2(1, 22);
var dim2 = init2(2, 22);
var italic2 = init2(3, 23);
var underline2 = init2(4, 24);
var inverse2 = init2(7, 27);
var hidden2 = init2(8, 28);
var strikethrough2 = init2(9, 29);
var black2 = init2(30, 39);
var red2 = init2(31, 39);
var green2 = init2(32, 39);
var yellow2 = init2(33, 39);
var blue2 = init2(34, 39);
var magenta2 = init2(35, 39);
var cyan2 = init2(36, 39);
var white2 = init2(37, 39);
var gray2 = init2(90, 39);
var grey2 = init2(90, 39);
var bgBlack2 = init2(40, 49);
var bgRed2 = init2(41, 49);
var bgGreen2 = init2(42, 49);
var bgYellow2 = init2(43, 49);
var bgBlue2 = init2(44, 49);
var bgMagenta2 = init2(45, 49);
var bgCyan2 = init2(46, 49);
var bgWhite2 = init2(47, 49);
var MAX_BIND_VALUES = 98;
var GENERIC_SQLITE_ERROR = 1;
function getColumnTypes(columnNames, rows) {
  const columnTypes = [];
  columnLoop: for (let columnIndex = 0; columnIndex < columnNames.length; columnIndex++) {
    for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
      const candidateValue = rows[rowIndex][columnIndex];
      if (candidateValue !== null) {
        const inferred = inferColumnType(candidateValue);
        if (columnTypes[columnIndex] === void 0 || inferred === ColumnTypeEnum.Text) {
          columnTypes[columnIndex] = inferred;
        }
        if (inferred !== ColumnTypeEnum.UnknownNumber) {
          continue columnLoop;
        }
      }
    }
    if (columnTypes[columnIndex] === void 0) {
      columnTypes[columnIndex] = ColumnTypeEnum.Int32;
    }
  }
  return columnTypes;
}
__name(getColumnTypes, "getColumnTypes");
function inferColumnType(value) {
  switch (typeof value) {
    case "string":
      return inferStringType(value);
    case "number":
      return inferNumberType(value);
    case "object":
      return inferObjectType(value);
    default:
      throw new UnexpectedTypeError(value);
  }
}
__name(inferColumnType, "inferColumnType");
var isoDateRegex = new RegExp(
  /^(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))$|^(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))$|^(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))$/
);
var sqliteDateRegex = /^\d{4}-[0-1]\d-[0-3]\d [0-2]\d:[0-5]\d:[0-5]\d$/;
function isISODate(str) {
  return isoDateRegex.test(str) || sqliteDateRegex.test(str);
}
__name(isISODate, "isISODate");
function inferStringType(value) {
  if (isISODate(value)) {
    return ColumnTypeEnum.DateTime;
  }
  return ColumnTypeEnum.Text;
}
__name(inferStringType, "inferStringType");
function inferNumberType(_) {
  return ColumnTypeEnum.UnknownNumber;
}
__name(inferNumberType, "inferNumberType");
function inferObjectType(value) {
  if (value instanceof Array) {
    return ColumnTypeEnum.Bytes;
  }
  throw new UnexpectedTypeError(value);
}
__name(inferObjectType, "inferObjectType");
var UnexpectedTypeError = class extends Error {
  static {
    __name(this, "UnexpectedTypeError");
  }
  name = "UnexpectedTypeError";
  constructor(value) {
    const type = typeof value;
    const repr = type === "object" ? JSON.stringify(value) : String(value);
    super(`unexpected value of type ${type}: ${repr}`);
  }
};
function mapRow(result, columnTypes) {
  for (let i = 0; i < result.length; i++) {
    const value = result[i];
    if (value instanceof ArrayBuffer) {
      result[i] = Array.from(new Uint8Array(value));
      continue;
    }
    if (typeof value === "number" && (columnTypes[i] === ColumnTypeEnum.Int32 || columnTypes[i] === ColumnTypeEnum.Int64) && !Number.isInteger(value)) {
      result[i] = Math.trunc(value);
      continue;
    }
    if (typeof value === "number" && columnTypes[i] === ColumnTypeEnum.Text) {
      result[i] = value.toString();
      continue;
    }
    if (typeof value === "bigint") {
      result[i] = value.toString();
      continue;
    }
    if (columnTypes[i] === ColumnTypeEnum.Boolean) {
      result[i] = JSON.parse(value);
    }
  }
  return result;
}
__name(mapRow, "mapRow");
function convertDriverError(error) {
  if (!isDbError(error)) {
    throw error;
  }
  let stripped = error.message.split("D1_ERROR: ").at(1) ?? error.message;
  stripped = stripped.split("SqliteError: ").at(1) ?? stripped;
  if (stripped.startsWith("UNIQUE constraint failed") || stripped.startsWith("PRIMARY KEY constraint failed")) {
    const fields = stripped.split(": ").at(1)?.split(", ").map((field) => field.split(".").pop());
    return {
      kind: "UniqueConstraintViolation",
      constraint: fields !== void 0 ? { fields } : void 0
    };
  } else if (stripped.startsWith("NOT NULL constraint failed")) {
    const fields = stripped.split(": ").at(1)?.split(", ").map((field) => field.split(".").pop());
    return {
      kind: "NullConstraintViolation",
      constraint: fields !== void 0 ? { fields } : void 0
    };
  } else if (stripped.startsWith("FOREIGN KEY constraint failed") || stripped.startsWith("CHECK constraint failed")) {
    return {
      kind: "ForeignKeyConstraintViolation",
      constraint: { foreignKey: {} }
    };
  } else if (stripped.startsWith("no such table")) {
    return {
      kind: "TableDoesNotExist",
      table: stripped.split(": ").at(1)
    };
  } else if (stripped.startsWith("no such column")) {
    return {
      kind: "ColumnNotFound",
      column: stripped.split(": ").at(1)
    };
  } else if (stripped.includes("has no column named ")) {
    return {
      kind: "ColumnNotFound",
      column: stripped.split("has no column named ").at(1)
    };
  }
  return {
    kind: "sqlite",
    extendedCode: error["code"] ?? error["cause"]?.["code"] ?? 1,
    message: error.message
  };
}
__name(convertDriverError, "convertDriverError");
function isDbError(error) {
  return typeof error.message === "string";
}
__name(isDbError, "isDbError");
function cleanArg(arg, argType) {
  if (arg !== null) {
    if (argType === "Int64" || typeof arg === "bigint") {
      const asInt56 = Number.parseInt(`${arg}`);
      if (!Number.isSafeInteger(asInt56)) {
        throw new Error(`Invalid Int64-encoded value received: ${arg}`);
      }
      return asInt56;
    }
    if (argType === "Int32") {
      return Number.parseInt(arg);
    }
    if (argType === "Float" || argType === "Double") {
      return Number.parseFloat(arg);
    }
    if (arg === true) {
      return 1;
    }
    if (arg === false) {
      return 0;
    }
    if (arg instanceof Uint8Array) {
      return Array.from(arg);
    }
  }
  return arg;
}
__name(cleanArg, "cleanArg");
var debug2 = Debug2("prisma:driver-adapter:d1-http");
function onUnsuccessfulD1HTTPResponse({ errors }) {
  debug2("D1 HTTP Errors: %O", errors);
  const error = errors.at(0) ?? { message: "Unknown error", code: GENERIC_SQLITE_ERROR };
  throw new DriverAdapterError(convertDriverError(error));
}
__name(onUnsuccessfulD1HTTPResponse, "onUnsuccessfulD1HTTPResponse");
function onGenericD1HTTPError(error) {
  debug2("HTTP Error: %O", error);
  throw new DriverAdapterError(convertDriverError(error));
}
__name(onGenericD1HTTPError, "onGenericD1HTTPError");
function onError(error) {
  console.error("Error in performIO: %O", error);
  throw new DriverAdapterError(convertDriverError(error));
}
__name(onError, "onError");
async function performRawQuery(client, options) {
  try {
    const response = await client.post("raw", options).json();
    const tag = "[js::performRawQuery]";
    debug2(`${tag} %O`, {
      success: response.success,
      errors: response.errors,
      messages: response.messages,
      result: response.result
    });
    if (!response.success) {
      onUnsuccessfulD1HTTPResponse(response);
    }
    return response.result;
  } catch (e) {
    onGenericD1HTTPError(e);
  }
}
__name(performRawQuery, "performRawQuery");
function isD1HTTPParams(params) {
  return typeof params === "object" && params !== null && "CLOUDFLARE_D1_TOKEN" in params && "CLOUDFLARE_ACCOUNT_ID" in params && "CLOUDFLARE_DATABASE_ID" in params;
}
__name(isD1HTTPParams, "isD1HTTPParams");
var D1HTTPQueryable = class {
  static {
    __name(this, "D1HTTPQueryable");
  }
  constructor(client) {
    this.client = client;
  }
  provider = "sqlite";
  adapterName = `${name}-http`;
  /**
   * Execute a query given as SQL, interpolating the given parameters.
   */
  async queryRaw(query) {
    const tag = "[js::query_raw]";
    debug2(`${tag} %O`, query);
    const data = await this.performIO(query);
    const convertedData = this.convertData(data);
    return convertedData;
  }
  convertData({ columnNames, rows: results }) {
    if (results.length === 0) {
      return {
        columnNames: [],
        columnTypes: [],
        rows: []
      };
    }
    const columnTypes = getColumnTypes(columnNames, results);
    const rows = results.map((value) => mapRow(value, columnTypes));
    return {
      columnNames,
      columnTypes,
      rows
    };
  }
  /**
   * Execute a query given as SQL, interpolating the given parameters and
   * returning the number of affected rows.
   * Note: Queryable expects a u64, but napi.rs only supports u32.
   */
  async executeRaw(query) {
    const tag = "[js::execute_raw]";
    debug2(`${tag} %O`, query);
    const result = await this.performIO(query);
    return result.affectedRows ?? 0;
  }
  async performIO(query) {
    try {
      query.args = query.args.map((arg, i) => cleanArg(arg, query.argTypes[i]));
      const body = {
        json: {
          sql: query.sql,
          params: query.args
        }
      };
      const tag = "[js::perform_io]";
      debug2(`${tag} %O`, body);
      const results = await performRawQuery(this.client, body);
      if (results.length !== 1) {
        throw new Error("Expected exactly one result");
      }
      const result = results[0];
      const { columns: columnNames = [], rows = [] } = result.results ?? {};
      const affectedRows = result.meta?.changes;
      return { rows, columnNames, affectedRows };
    } catch (e) {
      onError(e);
    }
  }
};
var D1HTTPTransaction = class extends D1HTTPQueryable {
  static {
    __name(this, "D1HTTPTransaction");
  }
  constructor(client, options) {
    super(client);
    this.options = options;
  }
  async commit() {
    debug2(`[js::commit]`);
  }
  async rollback() {
    debug2(`[js::rollback]`);
  }
};
var PrismaD1HTTPAdapter = class extends D1HTTPQueryable {
  static {
    __name(this, "PrismaD1HTTPAdapter");
  }
  constructor(params, release) {
    const D1_API_BASE_URL = `https://api.cloudflare.com/client/v4/accounts/${params.CLOUDFLARE_ACCOUNT_ID}/d1/database/${params.CLOUDFLARE_DATABASE_ID}`;
    const client = distribution_default.create({
      prefixUrl: D1_API_BASE_URL,
      headers: {
        Authorization: `Bearer ${params.CLOUDFLARE_D1_TOKEN}`
      },
      // Don't automatically throw on non-2xx status codes
      throwHttpErrors: false
    });
    super(client);
    this.release = release;
  }
  tags = {
    error: red2("prisma:error"),
    warn: yellow2("prisma:warn"),
    info: cyan2("prisma:info"),
    query: blue2("prisma:query")
  };
  alreadyWarned = /* @__PURE__ */ new Set();
  /**
   * This will warn once per transaction
   * e.g. the following two explicit transactions
   * will only trigger _two_ warnings
   *
   * ```ts
   * await prisma.$transaction([ ...queries ])
   * await prisma.$transaction([ ...moreQueries ])
   * ```
   */
  warnOnce = /* @__PURE__ */ __name((key, message, ...args) => {
    if (!this.alreadyWarned.has(key)) {
      this.alreadyWarned.add(key);
      console.info(`${this.tags.warn} ${message}`, ...args);
    }
  }, "warnOnce");
  async executeScript(script) {
    try {
      await performRawQuery(this.client, {
        json: {
          sql: script
        }
      });
    } catch (error) {
      onError(error);
    }
  }
  getConnectionInfo() {
    return {
      maxBindValues: MAX_BIND_VALUES,
      supportsRelationJoins: false
    };
  }
  async startTransaction(isolationLevel) {
    if (isolationLevel && isolationLevel !== "SERIALIZABLE") {
      throw new DriverAdapterError({
        kind: "InvalidIsolationLevel",
        level: isolationLevel
      });
    }
    this.warnOnce(
      "D1 Transaction",
      "Cloudflare D1 does not support transactions yet. When using Prisma's D1 adapter, implicit & explicit transactions will be ignored and run as individual queries, which breaks the guarantees of the ACID properties of transactions. For more details see https://pris.ly/d/d1-transactions"
    );
    const options = {
      usePhantomQuery: true
    };
    const tag = "[js::startTransaction]";
    debug2("%s options: %O", tag, options);
    return new D1HTTPTransaction(this.client, options);
  }
  async dispose() {
    await this.release?.();
  }
};
var PrismaD1HTTPAdapterFactory = class {
  static {
    __name(this, "PrismaD1HTTPAdapterFactory");
  }
  constructor(params) {
    this.params = params;
  }
  provider = "sqlite";
  adapterName = `${name}-http`;
  async connect() {
    return new PrismaD1HTTPAdapter(this.params, async () => {
    });
  }
  async connectToShadowDb() {
    const D1_API_BASE_URL = `https://api.cloudflare.com/client/v4/accounts/${this.params.CLOUDFLARE_ACCOUNT_ID}/d1/database`;
    const client = distribution_default.create({
      headers: {
        Authorization: `Bearer ${this.params.CLOUDFLARE_D1_TOKEN}`
      },
      // Don't throw on non-2xx status codes
      throwHttpErrors: false
    });
    const createShadowDatabase = /* @__PURE__ */ __name(async () => {
      const tag = "[js::connectToShadowDb::createShadowDatabase]";
      const SHADOW_DATABASE_PREFIX = "_prisma_shadow_";
      const CLOUDFLARE_SHADOW_DATABASE_NAME = `${SHADOW_DATABASE_PREFIX}${globalThis.crypto.randomUUID()}`;
      debug2(`${tag} creating database %s`, CLOUDFLARE_SHADOW_DATABASE_NAME);
      try {
        const response = await client.post(D1_API_BASE_URL, {
          json: {
            name: CLOUDFLARE_SHADOW_DATABASE_NAME
          }
        }).json();
        debug2(`${tag} %O`, response);
        if (!response.success) {
          onUnsuccessfulD1HTTPResponse(response);
        }
        const { uuid: CLOUDFLARE_SHADOW_DATABASE_ID2 } = response.result;
        debug2(`${tag} created database %s with ID %s`, CLOUDFLARE_SHADOW_DATABASE_NAME, CLOUDFLARE_SHADOW_DATABASE_ID2);
        return CLOUDFLARE_SHADOW_DATABASE_ID2;
      } catch (e) {
        onGenericD1HTTPError(e);
      }
    }, "createShadowDatabase");
    const CLOUDFLARE_SHADOW_DATABASE_ID = this.params.CLOUDFLARE_SHADOW_DATABASE_ID ?? await createShadowDatabase();
    const dispose = /* @__PURE__ */ __name(async () => {
      const tag = "[js::connectToShadowDb::dispose]";
      try {
        debug2(`${tag} deleting database %s`, CLOUDFLARE_SHADOW_DATABASE_ID);
        const response = await client.delete(`${D1_API_BASE_URL}/${CLOUDFLARE_SHADOW_DATABASE_ID}`).json();
        debug2(`${tag} %O`, response);
        if (!response.success) {
          onUnsuccessfulD1HTTPResponse(response);
        }
      } catch (e) {
        onGenericD1HTTPError(e);
      }
    }, "dispose");
    return new PrismaD1HTTPAdapter(this.params, dispose);
  }
};
var debug22 = Debug2("prisma:driver-adapter:d1");
var D1WorkerQueryable = class {
  static {
    __name(this, "D1WorkerQueryable");
  }
  constructor(client) {
    this.client = client;
  }
  provider = "sqlite";
  adapterName = name;
  /**
   * Execute a query given as SQL, interpolating the given parameters.
   */
  async queryRaw(query) {
    const tag = "[js::query_raw]";
    debug22(`${tag} %O`, query);
    const data = await this.performIO(query);
    const convertedData = this.convertData(data);
    return convertedData;
  }
  convertData(ioResult) {
    const columnNames = ioResult[0];
    const results = ioResult[1];
    if (results.length === 0) {
      return {
        columnNames: [],
        columnTypes: [],
        rows: []
      };
    }
    const columnTypes = Object.values(getColumnTypes(columnNames, results));
    const rows = results.map((value) => mapRow(value, columnTypes));
    return {
      columnNames,
      // * Note: without Object.values the array looks like
      // * columnTypes: [ id: 128 ],
      // * and errors with:
      // * ✘ [ERROR] A hanging Promise was canceled. This happens when the worker runtime is waiting for a Promise from JavaScript to resolve, but has detected that the Promise cannot possibly ever resolve because all code and events related to the Promise's I/O context have already finished.
      columnTypes,
      rows
    };
  }
  /**
   * Execute a query given as SQL, interpolating the given parameters and
   * returning the number of affected rows.
   * Note: Queryable expects a u64, but napi.rs only supports u32.
   */
  async executeRaw(query) {
    const tag = "[js::execute_raw]";
    debug22(`${tag} %O`, query);
    const result = await this.performIO(query, true);
    return result.meta.changes ?? 0;
  }
  async performIO(query, executeRaw = false) {
    try {
      query.args = query.args.map((arg, i) => cleanArg(arg, query.argTypes[i]));
      const stmt = this.client.prepare(query.sql).bind(...query.args);
      if (executeRaw) {
        return await stmt.run();
      } else {
        const [columnNames, ...rows] = await stmt.raw({ columnNames: true });
        return [columnNames, rows];
      }
    } catch (e) {
      onError2(e);
    }
  }
};
var D1WorkerTransaction = class extends D1WorkerQueryable {
  static {
    __name(this, "D1WorkerTransaction");
  }
  constructor(client, options) {
    super(client);
    this.options = options;
  }
  async commit() {
    debug22(`[js::commit]`);
  }
  async rollback() {
    debug22(`[js::rollback]`);
  }
};
var PrismaD1WorkerAdapter = class extends D1WorkerQueryable {
  static {
    __name(this, "PrismaD1WorkerAdapter");
  }
  constructor(client, release) {
    super(client);
    this.release = release;
  }
  tags = {
    error: red2("prisma:error"),
    warn: yellow2("prisma:warn"),
    info: cyan2("prisma:info"),
    query: blue2("prisma:query")
  };
  alreadyWarned = /* @__PURE__ */ new Set();
  /**
   * This will warn once per transaction
   * e.g. the following two explicit transactions
   * will only trigger _two_ warnings
   *
   * ```ts
   * await prisma.$transaction([ ...queries ])
   * await prisma.$transaction([ ...moreQueries ])
   * ```
   */
  warnOnce = /* @__PURE__ */ __name((key, message, ...args) => {
    if (!this.alreadyWarned.has(key)) {
      this.alreadyWarned.add(key);
      console.info(`${this.tags.warn} ${message}`, ...args);
    }
  }, "warnOnce");
  async executeScript(script) {
    try {
      await this.client.exec(script);
    } catch (error) {
      onError2(error);
    }
  }
  getConnectionInfo() {
    return {
      maxBindValues: MAX_BIND_VALUES,
      supportsRelationJoins: false
    };
  }
  async startTransaction(isolationLevel) {
    if (isolationLevel && isolationLevel !== "SERIALIZABLE") {
      throw new DriverAdapterError({
        kind: "InvalidIsolationLevel",
        level: isolationLevel
      });
    }
    this.warnOnce(
      "D1 Transaction",
      "Cloudflare D1 does not support transactions yet. When using Prisma's D1 adapter, implicit & explicit transactions will be ignored and run as individual queries, which breaks the guarantees of the ACID properties of transactions. For more details see https://pris.ly/d/d1-transactions"
    );
    const options = {
      usePhantomQuery: true
    };
    const tag = "[js::startTransaction]";
    debug22("%s options: %O", tag, options);
    return new D1WorkerTransaction(this.client, options);
  }
  async dispose() {
    await this.release?.();
  }
};
var PrismaD1WorkerAdapterFactory = class {
  static {
    __name(this, "PrismaD1WorkerAdapterFactory");
  }
  constructor(client) {
    this.client = client;
  }
  provider = "sqlite";
  adapterName = name;
  async connect() {
    return new PrismaD1WorkerAdapter(this.client, async () => {
    });
  }
};
function onError2(error) {
  console.error("Error in performIO: %O", error);
  throw new DriverAdapterError(convertDriverError(error));
}
__name(onError2, "onError2");
var PrismaD1 = class {
  static {
    __name(this, "PrismaD1");
  }
  provider = "sqlite";
  adapterName = name;
  connect;
  connectToShadowDb;
  constructor(params) {
    if (isD1HTTPParams(params)) {
      const factory = new PrismaD1HTTPAdapterFactory(params);
      const self2 = this;
      self2.connect = factory.connect.bind(factory);
      self2.connectToShadowDb = factory.connectToShadowDb.bind(factory);
    } else {
      const factory = new PrismaD1WorkerAdapterFactory(params);
      const self2 = this;
      self2.connect = factory.connect.bind(factory);
    }
  }
};

// src/utils/crypto.ts
init_modules_watch_stub();
function generateLicenseKey() {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  const segments = [];
  for (let i = 0; i < 4; i++) {
    let segment = "";
    for (let j = 0; j < 4; j++) {
      segment += chars[Math.floor(Math.random() * chars.length)];
    }
    segments.push(segment);
  }
  return segments.join("-");
}
__name(generateLicenseKey, "generateLicenseKey");
function validateLicenseKeyFormat(key) {
  const pattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
  return pattern.test(key);
}
__name(validateLicenseKeyFormat, "validateLicenseKeyFormat");
async function encryptVerifyInstance(data, key) {
  const dataStr = JSON.stringify(data);
  const encoder = new TextEncoder();
  const keyData = encoder.encode(key.slice(0, 32));
  const cryptoKey = await crypto.subtle.importKey(
    "raw",
    keyData,
    { name: "AES-GCM" },
    false,
    ["encrypt"]
  );
  const iv = crypto.getRandomValues(new Uint8Array(12));
  const encrypted = await crypto.subtle.encrypt(
    { name: "AES-GCM", iv },
    cryptoKey,
    encoder.encode(dataStr)
  );
  const combined = new Uint8Array(iv.length + encrypted.byteLength);
  combined.set(iv);
  combined.set(new Uint8Array(encrypted), iv.length);
  return btoa(String.fromCharCode(...combined));
}
__name(encryptVerifyInstance, "encryptVerifyInstance");
async function decryptVerifyInstance(encryptedData, key) {
  try {
    const combined = new Uint8Array(
      atob(encryptedData).split("").map((char) => char.charCodeAt(0))
    );
    const iv = combined.slice(0, 12);
    const encrypted = combined.slice(12);
    const encoder = new TextEncoder();
    const keyData = encoder.encode(key.slice(0, 32));
    const cryptoKey = await crypto.subtle.importKey(
      "raw",
      keyData,
      { name: "AES-GCM" },
      false,
      ["decrypt"]
    );
    const decrypted = await crypto.subtle.decrypt(
      { name: "AES-GCM", iv },
      cryptoKey,
      encrypted
    );
    const decoder = new TextDecoder();
    return JSON.parse(decoder.decode(decrypted));
  } catch (error) {
    throw new Error("Failed to decrypt verify instance");
  }
}
__name(decryptVerifyInstance, "decryptVerifyInstance");

// src/services/license.service.ts
var LicenseService = class {
  static {
    __name(this, "LicenseService");
  }
  constructor(db) {
    const adapter = new PrismaD1(db);
    this.prisma = new import_prisma.PrismaClient({ adapter });
  }
  /**
   * 生成许可证（支持单个和批量创建）
   */
  async createLicenses(request) {
    const { versionId, distributorId, verifyInstance, quantity = 1 } = request;
    const version = await this.prisma.productVersion.findUnique({
      where: { id: versionId },
      include: {
        product: true
      }
    });
    if (!version) {
      throw new Error("\u4EA7\u54C1\u7248\u672C\u4E0D\u5B58\u5728");
    }
    const licenses = [];
    for (let i = 0; i < quantity; i++) {
      const licenseKey = generateLicenseKey();
      const license = await this.prisma.license.create({
        data: {
          versionId,
          distributorId,
          licenseKey,
          verifyInstance: JSON.stringify(verifyInstance),
          status: "INACTIVE"
        }
      });
      licenses.push({
        id: license.id,
        licenseKey: license.licenseKey,
        status: license.status,
        createdAt: license.createdAt.toISOString()
      });
    }
    const productInfo = {
      id: version.product.id,
      name: version.product.name,
      version: version.version,
      versionName: version.versionName || void 0,
      description: version.description || void 0
    };
    return {
      licenses,
      product: productInfo,
      total: quantity
    };
  }
  /**
   * 获取许可证列表
   */
  async getLicenses(params) {
    const { distributorId, versionId, status, licenseKey, pagination } = params;
    const { page, limit } = pagination;
    const skip2 = (page - 1) * limit;
    const where = {};
    if (distributorId) where.distributorId = distributorId;
    if (versionId) where.versionId = versionId;
    if (status) where.status = status;
    if (licenseKey) where.licenseKey = { contains: licenseKey };
    const [licenses, total] = await Promise.all([
      this.prisma.license.findMany({
        where,
        include: {
          version: {
            include: {
              product: true
            }
          },
          distributor: true
        },
        skip: skip2,
        take: limit,
        orderBy: { createdAt: "desc" }
      }),
      this.prisma.license.count({ where })
    ]);
    return {
      licenses: licenses.map((license) => ({
        id: license.id,
        licenseKey: license.licenseKey,
        status: license.status,
        activatedAt: license.activatedAt?.toISOString(),
        createdAt: license.createdAt.toISOString(),
        product: {
          id: license.version.product.id,
          name: license.version.product.name,
          version: license.version.version,
          versionName: license.version.versionName || void 0
        },
        distributor: license.distributor.nickName || license.distributor.username
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }
  /**
   * 撤销许可证
   */
  async revokeLicense(licenseId) {
    const license = await this.prisma.license.findUnique({
      where: { id: licenseId }
    });
    if (!license) {
      throw new Error("\u8BB8\u53EF\u8BC1\u4E0D\u5B58\u5728");
    }
    if (license.status === "REVOKED") {
      throw new Error("\u8BB8\u53EF\u8BC1\u5DF2\u88AB\u64A4\u9500");
    }
    await this.prisma.license.update({
      where: { id: licenseId },
      data: { status: "REVOKED" }
    });
  }
};

// src/utils/response.ts
init_modules_watch_stub();
function createSuccessResponse(data, message = "\u64CD\u4F5C\u6210\u529F") {
  return {
    success: true,
    data,
    message,
    timestamp: (/* @__PURE__ */ new Date()).toISOString()
  };
}
__name(createSuccessResponse, "createSuccessResponse");
function createErrorResponse(code, message) {
  return {
    success: false,
    error: { code, message },
    timestamp: (/* @__PURE__ */ new Date()).toISOString()
  };
}
__name(createErrorResponse, "createErrorResponse");

// src/routes/license.ts
var licenseRoutes = new Hono2();
licenseRoutes.post("/licenses", async (c) => {
  try {
    const request = await c.req.json();
    if (!request.versionId || !request.distributorId || !request.verifyInstance) {
      return c.json(createErrorResponse("INVALID_PARAMS", "\u53C2\u6570\u9519\u8BEF\uFF1A\u7F3A\u5C11\u5FC5\u8981\u53C2\u6570"), 400);
    }
    const quantity = request.quantity || 1;
    if (quantity <= 0) {
      return c.json(createErrorResponse("INVALID_PARAMS", "\u6570\u91CF\u5FC5\u987B\u5927\u4E8E0"), 400);
    }
    const licenseService = new LicenseService(c.env.DB);
    const result = await licenseService.createLicenses(request);
    return c.json(createSuccessResponse(result, `\u6210\u529F\u751F\u6210 ${quantity} \u4E2A\u8BB8\u53EF\u8BC1`), 201);
  } catch (error) {
    console.error("\u751F\u6210\u8BB8\u53EF\u8BC1\u5931\u8D25:", error);
    const message = error instanceof Error ? error.message : "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF";
    return c.json(createErrorResponse("INTERNAL_ERROR", message), 500);
  }
});
licenseRoutes.get("/licenses", async (c) => {
  try {
    const query = c.req.query();
    const distributorId = query.distributorId ? parseInt(query.distributorId) : void 0;
    const versionId = query.versionId ? parseInt(query.versionId) : void 0;
    const { status, licenseKey } = query;
    const page = parseInt(query.page || "1");
    const limit = parseInt(query.limit || "20");
    const licenseService = new LicenseService(c.env.DB);
    const result = await licenseService.getLicenses({
      distributorId,
      versionId,
      status,
      licenseKey,
      pagination: { page, limit }
    });
    return c.json(createSuccessResponse(result));
  } catch (error) {
    console.error("\u83B7\u53D6\u8BB8\u53EF\u8BC1\u5217\u8868\u5931\u8D25:", error);
    const message = error instanceof Error ? error.message : "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF";
    return c.json(createErrorResponse("INTERNAL_ERROR", message), 500);
  }
});
licenseRoutes.post("/licenses/:id/revoke", async (c) => {
  try {
    const licenseId = parseInt(c.req.param("id"));
    const licenseService = new LicenseService(c.env.DB);
    await licenseService.revokeLicense(licenseId);
    return c.json(createSuccessResponse(null, "\u8BB8\u53EF\u8BC1\u64A4\u9500\u6210\u529F"));
  } catch (error) {
    console.error("\u64A4\u9500\u8BB8\u53EF\u8BC1\u5931\u8D25:", error);
    const message = error instanceof Error ? error.message : "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF";
    return c.json(createErrorResponse("INTERNAL_ERROR", message), 500);
  }
});

// src/routes/verify.ts
init_modules_watch_stub();

// src/services/verify.service.ts
init_modules_watch_stub();
var import_prisma2 = __toESM(require_default());
var VerifyService = class {
  static {
    __name(this, "VerifyService");
  }
  constructor(db) {
    const adapter = new PrismaD1(db);
    this.prisma = new import_prisma2.PrismaClient({ adapter });
  }
  /**
   * 验证许可证并获取配置（支持加密）
   */
  async verifyLicense(request) {
    const { licenseKey, verifyInstance } = request;
    if (!validateLicenseKeyFormat(licenseKey)) {
      return {
        valid: false,
        reason: "INVALID_FORMAT",
        message: "\u65E0\u6548\u7684\u8BB8\u53EF\u8BC1\u5BC6\u94A5\u683C\u5F0F"
      };
    }
    const license = await this.prisma.license.findUnique({
      where: { licenseKey },
      include: {
        version: {
          include: {
            product: true
          }
        }
      }
    });
    if (!license) {
      return {
        valid: false,
        reason: "LICENSE_NOT_FOUND",
        message: "\u8BB8\u53EF\u8BC1\u4E0D\u5B58\u5728"
      };
    }
    if (license.status === "REVOKED") {
      return {
        valid: false,
        reason: "LICENSE_REVOKED",
        message: "\u8BB8\u53EF\u8BC1\u5DF2\u88AB\u64A4\u9500"
      };
    }
    const productInfo = {
      id: license.version.product.id,
      name: license.version.product.name,
      version: license.version.version,
      versionName: license.version.versionName || void 0,
      description: license.version.description || void 0
    };
    const clientConfig = license.verifyInstance ? JSON.parse(license.verifyInstance) : {};
    if (license.status === "INACTIVE") {
      await this.prisma.license.update({
        where: { id: license.id },
        data: {
          status: "ACTIVE",
          activatedAt: /* @__PURE__ */ new Date()
        }
      });
      const responseData2 = {
        valid: true,
        productInfo,
        verifyInstance: clientConfig,
        activatedAt: (/* @__PURE__ */ new Date()).toISOString(),
        lastFetched: (/* @__PURE__ */ new Date()).toISOString(),
        message: "\u8BB8\u53EF\u8BC1\u6FC0\u6D3B\u6210\u529F"
      };
      const encryptedResponse2 = await encryptVerifyInstance(responseData2, license.version.encryptionKey);
      return {
        valid: true,
        encrypted: true,
        data: encryptedResponse2,
        message: "\u8BB8\u53EF\u8BC1\u6FC0\u6D3B\u6210\u529F"
      };
    }
    const responseData = {
      valid: true,
      productInfo,
      verifyInstance: clientConfig,
      activatedAt: license.activatedAt?.toISOString(),
      lastFetched: (/* @__PURE__ */ new Date()).toISOString(),
      message: "\u914D\u7F6E\u83B7\u53D6\u6210\u529F"
    };
    const encryptedResponse = await encryptVerifyInstance(responseData, license.version.encryptionKey);
    return {
      valid: true,
      encrypted: true,
      data: encryptedResponse,
      message: "\u914D\u7F6E\u83B7\u53D6\u6210\u529F"
    };
  }
  /**
   * 解密验证请求（如果需要）
   */
  async decryptRequest(encryptedData, encryptionKey) {
    try {
      return await decryptVerifyInstance(encryptedData, encryptionKey);
    } catch (error) {
      throw new Error("\u8BF7\u6C42\u89E3\u5BC6\u5931\u8D25");
    }
  }
};

// src/routes/verify.ts
var verifyRoutes = new Hono2();
verifyRoutes.post("/verify", async (c) => {
  try {
    const request = await c.req.json();
    if (!request.licenseKey) {
      return c.json(createErrorResponse("INVALID_PARAMS", "\u53C2\u6570\u9519\u8BEF\uFF1A\u7F3A\u5C11licenseKey"), 400);
    }
    const verifyService = new VerifyService(c.env.DB);
    const result = await verifyService.verifyLicense(request);
    if (!result.valid) {
      return c.json(createErrorResponse(result.reason || "VERIFICATION_FAILED", result.message), 400);
    }
    return c.json(createSuccessResponse(result, result.message));
  } catch (error) {
    console.error("\u8BB8\u53EF\u8BC1\u9A8C\u8BC1\u5931\u8D25:", error);
    const message = error instanceof Error ? error.message : "\u670D\u52A1\u5668\u5185\u90E8\u9519\u8BEF";
    return c.json(createErrorResponse("INTERNAL_ERROR", message), 500);
  }
});
verifyRoutes.post("/verify/decrypt", async (c) => {
  try {
    const { encryptedData, licenseKey } = await c.req.json();
    if (!encryptedData || !licenseKey) {
      return c.json(createErrorResponse("INVALID_PARAMS", "\u53C2\u6570\u9519\u8BEF\uFF1A\u7F3A\u5C11\u52A0\u5BC6\u6570\u636E\u6216\u8BB8\u53EF\u8BC1\u5BC6\u94A5"), 400);
    }
    const verifyService = new VerifyService(c.env.DB);
    const basicVerifyResult = await verifyService.verifyLicense({ licenseKey });
    if (!basicVerifyResult.valid) {
      return c.json(createErrorResponse("INVALID_LICENSE", "\u65E0\u6548\u7684\u8BB8\u53EF\u8BC1"), 400);
    }
    const decryptedRequest = await verifyService.decryptRequest(encryptedData, "encryption_key");
    const result = await verifyService.verifyLicense(decryptedRequest);
    return c.json(createSuccessResponse(result, result.message));
  } catch (error) {
    console.error("\u89E3\u5BC6\u9A8C\u8BC1\u5931\u8D25:", error);
    const message = error instanceof Error ? error.message : "\u89E3\u5BC6\u6216\u9A8C\u8BC1\u5931\u8D25";
    return c.json(createErrorResponse("DECRYPT_ERROR", message), 500);
  }
});

// src/index.ts
var app = new Hono2();
app.use("*", cors({
  origin: ["http://localhost:5173", "https://verify.example.com"],
  allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowHeaders: ["Content-Type", "Authorization"]
}));
app.get("/", (c) => {
  return c.json({
    message: "License Verification Service API",
    version: "1.0.0",
    timestamp: (/* @__PURE__ */ new Date()).toISOString()
  });
});
app.route("/api", licenseRoutes);
app.route("/api", verifyRoutes);
var src_default = app;

// node_modules/.pnpm/wrangler@4.27.0/node_modules/wrangler/templates/middleware/middleware-ensure-req-body-drained.ts
init_modules_watch_stub();
var drainBody = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } finally {
    try {
      if (request.body !== null && !request.bodyUsed) {
        const reader = request.body.getReader();
        while (!(await reader.read()).done) {
        }
      }
    } catch (e) {
      console.error("Failed to drain the unused request body.", e);
    }
  }
}, "drainBody");
var middleware_ensure_req_body_drained_default = drainBody;

// node_modules/.pnpm/wrangler@4.27.0/node_modules/wrangler/templates/middleware/middleware-miniflare3-json-error.ts
init_modules_watch_stub();
function reduceError(e) {
  return {
    name: e?.name,
    message: e?.message ?? String(e),
    stack: e?.stack,
    cause: e?.cause === void 0 ? void 0 : reduceError(e.cause)
  };
}
__name(reduceError, "reduceError");
var jsonError = /* @__PURE__ */ __name(async (request, env, _ctx, middlewareCtx) => {
  try {
    return await middlewareCtx.next(request, env);
  } catch (e) {
    const error = reduceError(e);
    return Response.json(error, {
      status: 500,
      headers: { "MF-Experimental-Error-Stack": "true" }
    });
  }
}, "jsonError");
var middleware_miniflare3_json_error_default = jsonError;

// .wrangler/tmp/bundle-4PegfM/middleware-insertion-facade.js
var __INTERNAL_WRANGLER_MIDDLEWARE__ = [
  middleware_ensure_req_body_drained_default,
  middleware_miniflare3_json_error_default
];
var middleware_insertion_facade_default = src_default;

// node_modules/.pnpm/wrangler@4.27.0/node_modules/wrangler/templates/middleware/common.ts
init_modules_watch_stub();
var __facade_middleware__ = [];
function __facade_register__(...args) {
  __facade_middleware__.push(...args.flat());
}
__name(__facade_register__, "__facade_register__");
function __facade_invokeChain__(request, env, ctx, dispatch, middlewareChain) {
  const [head, ...tail] = middlewareChain;
  const middlewareCtx = {
    dispatch,
    next(newRequest, newEnv) {
      return __facade_invokeChain__(newRequest, newEnv, ctx, dispatch, tail);
    }
  };
  return head(request, env, ctx, middlewareCtx);
}
__name(__facade_invokeChain__, "__facade_invokeChain__");
function __facade_invoke__(request, env, ctx, dispatch, finalMiddleware) {
  return __facade_invokeChain__(request, env, ctx, dispatch, [
    ...__facade_middleware__,
    finalMiddleware
  ]);
}
__name(__facade_invoke__, "__facade_invoke__");

// .wrangler/tmp/bundle-4PegfM/middleware-loader.entry.ts
var __Facade_ScheduledController__ = class ___Facade_ScheduledController__ {
  constructor(scheduledTime, cron, noRetry) {
    this.scheduledTime = scheduledTime;
    this.cron = cron;
    this.#noRetry = noRetry;
  }
  static {
    __name(this, "__Facade_ScheduledController__");
  }
  #noRetry;
  noRetry() {
    if (!(this instanceof ___Facade_ScheduledController__)) {
      throw new TypeError("Illegal invocation");
    }
    this.#noRetry();
  }
};
function wrapExportedHandler(worker) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return worker;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  const fetchDispatcher = /* @__PURE__ */ __name(function(request, env, ctx) {
    if (worker.fetch === void 0) {
      throw new Error("Handler does not export a fetch() function.");
    }
    return worker.fetch(request, env, ctx);
  }, "fetchDispatcher");
  return {
    ...worker,
    fetch(request, env, ctx) {
      const dispatcher = /* @__PURE__ */ __name(function(type, init3) {
        if (type === "scheduled" && worker.scheduled !== void 0) {
          const controller = new __Facade_ScheduledController__(
            Date.now(),
            init3.cron ?? "",
            () => {
            }
          );
          return worker.scheduled(controller, env, ctx);
        }
      }, "dispatcher");
      return __facade_invoke__(request, env, ctx, dispatcher, fetchDispatcher);
    }
  };
}
__name(wrapExportedHandler, "wrapExportedHandler");
function wrapWorkerEntrypoint(klass) {
  if (__INTERNAL_WRANGLER_MIDDLEWARE__ === void 0 || __INTERNAL_WRANGLER_MIDDLEWARE__.length === 0) {
    return klass;
  }
  for (const middleware of __INTERNAL_WRANGLER_MIDDLEWARE__) {
    __facade_register__(middleware);
  }
  return class extends klass {
    #fetchDispatcher = /* @__PURE__ */ __name((request, env, ctx) => {
      this.env = env;
      this.ctx = ctx;
      if (super.fetch === void 0) {
        throw new Error("Entrypoint class does not define a fetch() function.");
      }
      return super.fetch(request);
    }, "#fetchDispatcher");
    #dispatcher = /* @__PURE__ */ __name((type, init3) => {
      if (type === "scheduled" && super.scheduled !== void 0) {
        const controller = new __Facade_ScheduledController__(
          Date.now(),
          init3.cron ?? "",
          () => {
          }
        );
        return super.scheduled(controller);
      }
    }, "#dispatcher");
    fetch(request) {
      return __facade_invoke__(
        request,
        this.env,
        this.ctx,
        this.#dispatcher,
        this.#fetchDispatcher
      );
    }
  };
}
__name(wrapWorkerEntrypoint, "wrapWorkerEntrypoint");
var WRAPPED_ENTRY;
if (typeof middleware_insertion_facade_default === "object") {
  WRAPPED_ENTRY = wrapExportedHandler(middleware_insertion_facade_default);
} else if (typeof middleware_insertion_facade_default === "function") {
  WRAPPED_ENTRY = wrapWorkerEntrypoint(middleware_insertion_facade_default);
}
var middleware_loader_entry_default = WRAPPED_ENTRY;
export {
  __INTERNAL_WRANGLER_MIDDLEWARE__,
  middleware_loader_entry_default as default
};
/*! Bundled license information:

ky/distribution/index.js:
  (*! MIT License © Sindre Sorhus *)
*/
//# sourceMappingURL=index.js.map
