/**
 * 许可证创建和验证流程模拟测试
 */

// 模拟API客户端
class ApiClient {
  constructor(baseUrl = 'http://localhost:8787/api') {
    this.baseUrl = baseUrl;
  }

  async post(endpoint, data) {
    console.log(`\n🔄 POST ${this.baseUrl}${endpoint}`);
    console.log('📤 请求数据:', JSON.stringify(data, null, 2));
    
    // 这里模拟API响应，实际使用时替换为真实的fetch调用
    return this.mockApiResponse(endpoint, data);
  }

  // 模拟API响应
  mockApiResponse(endpoint, data) {
    if (endpoint === '/licenses') {
      return this.mockCreateLicense(data);
    } else if (endpoint === '/verify') {
      return this.mockVerifyLicense(data);
    }
    throw new Error(`未知的端点: ${endpoint}`);
  }

  mockCreateLicense(data) {
    const { versionId, distributorId, verifyInstance, quantity = 1 } = data;
    
    const licenses = [];
    for (let i = 0; i < quantity; i++) {
      licenses.push({
        id: Math.floor(Math.random() * 10000),
        licenseKey: this.generateLicenseKey(),
        status: 'INACTIVE',
        createdAt: new Date().toISOString()
      });
    }

    return {
      success: true,
      data: {
        licenses,
        product: {
          id: versionId,
          name: 'TestProduct',
          version: '1.0.0',
          versionName: '正式版'
        },
        total: quantity
      },
      message: `成功生成 ${quantity} 个许可证`,
      timestamp: new Date().toISOString()
    };
  }

  mockVerifyLicense(data) {
    const { licenseKey, verifyInstance } = data;

    // 模拟加密响应
    const responseData = {
      valid: true,
      productInfo: {
        id: 1,
        name: 'TestProduct',
        version: '1.0.0',
        versionName: '正式版'
      },
      verifyInstance: {
        expiration: '2025-12-31',
        maxDevices: 3,
        features: ['feature1', 'feature2'],
        customField: 'test_value'
      },
      activatedAt: new Date().toISOString(),
      lastFetched: new Date().toISOString(),
      message: '许可证激活成功'
    };

    // 模拟加密数据（实际应该是加密后的字符串）
    // 在Node.js中使用Buffer.from().toString('base64')替代btoa
    const mockEncryptedData = Buffer.from(JSON.stringify(responseData), 'utf8').toString('base64');

    return {
      success: true,
      data: {
        valid: true,
        encrypted: true,
        data: mockEncryptedData,
        message: '许可证激活成功'
      },
      timestamp: new Date().toISOString()
    };
  }

  generateLicenseKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const segments = [];
    
    for (let i = 0; i < 4; i++) {
      let segment = '';
      for (let j = 0; j < 4; j++) {
        segment += chars[Math.floor(Math.random() * chars.length)];
      }
      segments.push(segment);
    }
    
    return segments.join('-');
  }
}

// 客户端验证逻辑
class LicenseValidator {
  static validate(config, clientInfo) {
    console.log('\n🔍 开始本地验证...');
    console.log('📋 验证配置:', JSON.stringify(config, null, 2));
    console.log('💻 客户端信息:', JSON.stringify(clientInfo, null, 2));

    // 检查过期时间
    if (new Date() > new Date(config.expiration)) {
      return { valid: false, reason: 'expired', message: '许可证已过期' };
    }
    console.log('✅ 过期时间检查通过');

    // 检查设备数量限制
    if (clientInfo.deviceCount > config.maxDevices) {
      return { 
        valid: false, 
        reason: 'device_limit_exceeded', 
        message: `设备数量超限 (${clientInfo.deviceCount}/${config.maxDevices})` 
      };
    }
    console.log('✅ 设备数量检查通过');

    // 检查功能权限
    for (const feature of clientInfo.requestedFeatures) {
      if (!config.features.includes(feature)) {
        return { 
          valid: false, 
          reason: 'feature_not_authorized', 
          message: `功能未授权: ${feature}` 
        };
      }
    }
    console.log('✅ 功能权限检查通过');

    // 自定义验证逻辑
    if (config.customField !== clientInfo.expectedCustomValue) {
      return { 
        valid: false, 
        reason: 'custom_validation_failed', 
        message: '自定义验证失败' 
      };
    }
    console.log('✅ 自定义验证通过');

    return { valid: true, message: '所有验证通过' };
  }
}

// 主要的模拟流程
async function simulateWorkflow() {
  console.log('🚀 开始模拟许可证工作流程\n');
  
  const apiClient = new ApiClient();

  // 步骤1: 创建单个许可证
  console.log('='.repeat(60));
  console.log('📝 步骤 1: 创建单个许可证');
  console.log('='.repeat(60));

  const singleLicenseRequest = {
    versionId: 1,
    distributorId: 2,
    verifyInstance: {
      expiration: '2025-12-31',
      maxDevices: 3,
      features: ['feature1', 'feature2'],
      customField: 'test_value'
    }
  };

  const singleResult = await apiClient.post('/licenses', singleLicenseRequest);
  console.log('📥 响应:', JSON.stringify(singleResult, null, 2));

  // 步骤2: 批量创建许可证
  console.log('\n' + '='.repeat(60));
  console.log('📝 步骤 2: 批量创建许可证');
  console.log('='.repeat(60));

  const batchLicenseRequest = {
    versionId: 1,
    distributorId: 2,
    quantity: 3,
    verifyInstance: {
      expiration: '2025-12-31',
      maxDevices: 5,
      features: ['feature1', 'feature2', 'feature3'],
      customField: 'batch_value'
    }
  };

  const batchResult = await apiClient.post('/licenses', batchLicenseRequest);
  console.log('📥 响应:', JSON.stringify(batchResult, null, 2));

  // 步骤3: 客户端验证许可证
  console.log('\n' + '='.repeat(60));
  console.log('🔐 步骤 3: 客户端验证许可证');
  console.log('='.repeat(60));

  const licenseKey = singleResult.data.licenses[0].licenseKey;
  const verifyRequest = {
    licenseKey: licenseKey
    // verifyInstance 现在是可选的
  };

  const verifyResult = await apiClient.post('/verify', verifyRequest);
  console.log('📥 验证响应:', JSON.stringify(verifyResult, null, 2));

  // 步骤4: 解密响应数据
  console.log('\n' + '='.repeat(60));
  console.log('🔓 步骤 4: 解密响应数据');
  console.log('='.repeat(60));

  let decryptedData;
  if (verifyResult.data.encrypted) {
    // 模拟解密过程（实际应该使用真实的解密算法）
    const encryptedData = verifyResult.data.data;
    // 在Node.js中使用Buffer.from().toString()替代atob
    decryptedData = JSON.parse(Buffer.from(encryptedData, 'base64').toString('utf8'));
    console.log('🔓 解密成功:', JSON.stringify(decryptedData, null, 2));
  } else {
    decryptedData = verifyResult.data;
  }

  // 步骤5: 客户端本地验证
  console.log('\n' + '='.repeat(60));
  console.log('💻 步骤 5: 客户端本地验证');
  console.log('='.repeat(60));

  const clientInfo = {
    deviceCount: 2,
    requestedFeatures: ['feature1', 'feature2'],
    expectedCustomValue: 'test_value'
  };

  const validationResult = LicenseValidator.validate(
    decryptedData.verifyInstance,
    clientInfo
  );

  console.log('\n🎯 最终验证结果:', validationResult);

  // 步骤6: 模拟验证失败的情况
  console.log('\n' + '='.repeat(60));
  console.log('❌ 步骤 6: 模拟验证失败情况');
  console.log('='.repeat(60));

  const invalidClientInfo = {
    deviceCount: 5, // 超过限制
    requestedFeatures: ['feature1', 'feature2', 'feature4'], // 未授权功能
    expectedCustomValue: 'wrong_value' // 错误的自定义值
  };

  const failedValidation = LicenseValidator.validate(
    decryptedData.verifyInstance,
    invalidClientInfo
  );

  console.log('\n🎯 失败验证结果:', failedValidation);

  console.log('\n' + '='.repeat(60));
  console.log('✨ 工作流程模拟完成');
  console.log('='.repeat(60));
}

// 运行模拟
if (require.main === module) {
  simulateWorkflow().catch(console.error);
}

module.exports = { ApiClient, LicenseValidator, simulateWorkflow };
